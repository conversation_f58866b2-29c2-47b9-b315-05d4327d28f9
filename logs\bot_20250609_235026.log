2025-06-09 23:50:26,095 - __main__ - INFO - Successfully configured console output with UTF-8 encoding
2025-06-09 23:50:26,096 - __main__ - INFO - Starting bot...
2025-06-09 23:50:26,097 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 10.0 RPS
2025-06-09 23:50:26,097 - __main__ - INFO - [SUCCESS] Simple pump analyzer functions initialized successfully
2025-06-09 23:50:26,098 - config_manager - INFO - Loading config from: C:\Users\<USER>\OneDrive\Documents\Another one test working - Copy1\finalconfig.json
2025-06-09 23:50:26,099 - state_manager - INFO - Attempting to load state from Main state file at sessions/trading_sim_session.session...
2025-06-09 23:50:26,100 - state_manager - INFO - Successfully loaded state from sessions/trading_sim_session.session (UTF-8 JSON).
2025-06-09 23:50:26,100 - state_manager - INFO - Loaded state data saved at 2025-06-09 23:45:41
2025-06-09 23:50:26,100 - state_manager - INFO - Loaded starting SOL from state: 1.5
2025-06-09 23:50:26,100 - state_manager - INFO - Recalculated available SOL: 1.5000
2025-06-09 23:50:26,102 - pumpportal_trader - INFO - Loaded transaction settings: {'slippage_percent': 1.0, 'buy_tip_sol': 0.005, 'handling_fee_percent': 1.0, 'gas_price_sol': 0.005, 'platform_fee_percent': 1, 'use_adaptive_slippage': True}
2025-06-09 23:50:26,103 - pumpportal_trader - INFO - Found default wallet: DEMO
2025-06-09 23:50:26,103 - pumpportal_trader - INFO - PumpPortal Trader initialized for real mode
2025-06-09 23:50:26,104 - pumpportal_trader - INFO - API URL: https://pumpportal.fun/api/trade-local
2025-06-09 23:50:26,104 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-09 23:50:26,104 - pumpportal_trader - INFO - Helius rate limiter: 10.0 RPS
2025-06-09 23:50:26,104 - trade_executor - INFO - TradeExecutor initialized with PumpPortal trader for real mode
2025-06-09 23:50:26,105 - __main__ - INFO - [SUCCESS] Fast analysis function connected to bot controller
2025-06-09 23:50:26,105 - __main__ - INFO - Initializing DEX adapters and sessions
2025-06-09 23:50:26,105 - __main__ - INFO - DEX sessions initialized successfully
2025-06-09 23:50:26,105 - __main__ - INFO - LLM integration is not available in this configuration
2025-06-09 23:50:26,105 - __main__ - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-09 23:50:26,106 - __main__ - INFO - LLM integration is not available in this configuration
2025-06-09 23:50:26,106 - __main__ - INFO - Initializing bot controller with position monitoring...
2025-06-09 23:50:26,106 - __main__ - INFO - [SUCCESS] Bot controller initialized successfully with position monitoring
2025-06-09 23:50:26,106 - __main__ - INFO - Started cache cleanup task
2025-06-09 23:50:26,106 - __main__ - INFO - Starting CLI interface...
2025-06-09 23:50:33,882 - state_manager - INFO - State saved successfully to sessions/trading_sim_session.session
2025-06-09 23:51:02,648 - websocket_manager - INFO - WebSocket disconnected
