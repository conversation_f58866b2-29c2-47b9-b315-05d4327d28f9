#!/usr/bin/env python3
"""
Test sell functionality on current position
"""

import asyncio
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_sell_current_position():
    """Test sell functionality on current position"""
    try:
        print("🔥 TESTING SELL FUNCTIONALITY ON CURRENT POSITION")
        print("=" * 60)
        
        # Import required modules
        from config_manager import ConfigManager
        from state_manager import StateManager
        from execution_queue import TradeExecutionQueue
        from bot_controller import BotController
        
        print("✅ All modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        state = StateManager(config)
        
        # Check current positions
        open_positions = state.get_open_positions()
        print(f"📊 Current open positions: {len(open_positions)}")
        
        if not open_positions:
            print("❌ No open positions found to test sell")
            return False
        
        # Show position details
        for token, position in open_positions.items():
            print(f"\n📊 Position: {token}")
            print(f"   💰 Token amount: {position.get('token_amount', 0):.6f}")
            print(f"   💵 Entry price: ${position.get('entry_price', 0):.8f}")
            print(f"   📅 Purchase time: {datetime.fromtimestamp(position.get('purchase_time', 0))}")
            print(f"   ⏰ Age: {(time.time() - position.get('purchase_time', 0))/60:.1f} minutes")
        
        # Initialize bot controller
        bot_controller = BotController(config, state)
        await bot_controller.initialize(enable_signal_processing=False)
        
        print("✅ Bot controller initialized")
        
        # Check execution queue status
        execution_queue = bot_controller.execution_queue
        print(f"🔄 Execution queue running: {execution_queue.is_running}")
        
        if not execution_queue.is_running:
            print("🚨 Starting execution queue...")
            await execution_queue.start()
            print("✅ Execution queue started")
        
        # Test sell on first position
        test_token = list(open_positions.keys())[0]
        test_position = open_positions[test_token]
        
        print(f"\n🔥 TESTING SELL ON: {test_token}")
        print("=" * 50)
        
        # Queue sell order
        sell_start_time = time.time()
        try:
            sell_result = await execution_queue.queue_sell(
                token_address=test_token,
                token_amount=test_position.get('token_amount', 0),
                entry_price=test_position.get('entry_price', 0),
                slippage_bps=1500,  # 15% slippage
                sell_fraction=1.0,  # Sell 100%
                priority=0,  # Emergency priority
                event_id=f"test_sell_{int(sell_start_time)}"
            )
            
            if sell_result:
                print("✅ Sell order queued successfully")
                
                # Wait for sell to complete
                print("⏳ Waiting for sell execution...")
                for i in range(60):  # Wait up to 60 seconds
                    await asyncio.sleep(1)
                    current_positions = state.get_open_positions()
                    
                    if test_token not in current_positions:
                        print(f"✅ SELL SUCCESSFUL! Position closed.")
                        print(f"⏱️  Sell execution time: {time.time() - sell_start_time:.2f} seconds")
                        
                        # Check final state
                        final_positions = state.get_open_positions()
                        print(f"📊 Final positions: {len(final_positions)}")
                        
                        return True
                    
                    print(f"   ⏳ Waiting... ({i+1}/60)")
                
                print("❌ SELL FAILED: Position still open after 60 seconds")
                return False
                
            else:
                print("❌ SELL FAILED: Could not queue sell order")
                return False
                
        except Exception as e:
            print(f"❌ SELL ERROR: {e}")
            logger.error(f"Sell test failed: {e}", exc_info=True)
            return False
        
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        logger.error(f"Sell test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = asyncio.run(test_sell_current_position())
    if result:
        print("\n🎉 SELL TEST SUCCESSFUL!")
    else:
        print("\n💥 SELL TEST FAILED!")
