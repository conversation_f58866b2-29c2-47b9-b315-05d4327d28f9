#!/usr/bin/env python3
"""
Diagnostic script to test sell functionality
This script will help identify if there are any issues with the sell execution pipeline
"""

import asyncio
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_sell_pipeline():
    """Test the sell execution pipeline"""
    try:
        print("🔍 SELL FUNCTIONALITY DIAGNOSTIC TEST")
        print("=" * 50)
        
        # Import required modules
        from config_manager import ConfigManager
        from state_manager import StateManager
        from trade_executor import TradeExecutor
        from execution_queue import TradeExecutionQueue
        from bot_controller import BotController
        
        print("✅ All modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        state = StateManager(config)
        
        print("✅ Config and State managers initialized")
        
        # Check current positions
        open_positions = state.get_open_positions()
        print(f"📊 Current open positions: {len(open_positions)}")
        
        if open_positions:
            print("🔍 Open positions found:")
            for token, position in open_positions.items():
                print(f"  • {token}: {position.get('token_amount', 0):.6f} tokens")
                print(f"    Entry: ${position.get('entry_price', 0):.8f}")
                print(f"    Status: {position.get('status', 'unknown')}")
                print(f"    Purchase time: {datetime.fromtimestamp(position.get('purchase_time', 0))}")
        else:
            print("ℹ️ No open positions found")
        
        # Check execution queue status
        execution_queue = TradeExecutionQueue(None, state)
        print(f"📋 Execution queue initialized: {execution_queue}")

        # Check if execution queue is running
        if hasattr(execution_queue, 'is_running'):
            print(f"🔄 Execution queue running: {execution_queue.is_running}")

        # Check sell queue status
        if hasattr(execution_queue, 'sell_queue'):
            sell_queue_size = execution_queue.sell_queue.qsize()
            print(f"📤 Sell queue size: {sell_queue_size}")
        
        # Check recent sell notifications
        import os
        sell_notifications_dir = "logs/sell_notifications"
        if os.path.exists(sell_notifications_dir):
            sell_files = [f for f in os.listdir(sell_notifications_dir) if f.startswith('sell_msg_')]
            sell_files.sort(key=lambda x: os.path.getmtime(os.path.join(sell_notifications_dir, x)), reverse=True)
            
            print(f"📨 Recent sell notifications: {len(sell_files[:5])}")
            for i, file in enumerate(sell_files[:5]):
                file_path = os.path.join(sell_notifications_dir, file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"  {i+1}. {file} - {mod_time}")
                
                # Read the first few lines to check content
                try:
                    with open(file_path, 'r') as f:
                        lines = f.readlines()[:10]
                        for line in lines:
                            if 'Transaction' in line and 'solscan.io' in line:
                                print(f"     ✅ Contains transaction link: {line.strip()}")
                                break
                except Exception as e:
                    print(f"     ❌ Error reading file: {e}")
        
        # Test configuration
        print("\n🔧 CONFIGURATION CHECK")
        print("=" * 30)
        
        trading_settings = config.get_section('trading_settings')
        max_hold_time = trading_settings.get('max_hold_time_minutes', 0)
        enforce_max_hold = trading_settings.get('enforce_max_hold_time', False)
        
        print(f"⏰ Max hold time: {max_hold_time} minutes")
        print(f"🔒 Enforce max hold time: {enforce_max_hold}")
        
        # Check TP/SL settings
        strategies = trading_settings.get('strategies', {})
        default_strategy = trading_settings.get('default_strategy_name', 'DEFAULT')
        
        if default_strategy in strategies:
            strategy = strategies[default_strategy]
            tp = strategy.get('take_profit_percent', 0)
            sl = strategy.get('stop_loss_percent', 0)
            print(f"🎯 Default strategy: {default_strategy}")
            print(f"📈 Take profit: {tp}%")
            print(f"📉 Stop loss: {sl}%")
        
        print("\n🔍 SELL TRIGGER CONDITIONS")
        print("=" * 35)
        print("The following conditions should trigger sells:")
        print(f"1. Take profit reached: +{tp}%")
        print(f"2. Stop loss hit: {sl}%")
        print(f"3. Max hold time exceeded: {max_hold_time} minutes")
        print("4. Rug pull detected (liquidity drain)")
        print("5. Emergency conditions")
        
        print("\n✅ DIAGNOSTIC COMPLETE")
        print("=" * 25)
        print("If sells are not working, check:")
        print("1. Console output for sell execution logs")
        print("2. Recent sell notification files")
        print("3. Transaction signatures on Solscan")
        print("4. Position monitoring is running")
        print("5. Execution queue is processing orders")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in diagnostic test: {e}")
        logger.error(f"Diagnostic test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    asyncio.run(test_sell_pipeline())
