import logging
import time
from typing import Optional
from pumpportal_trader import PumpPortalTrader
from utils import (
    log_trade,
    log_performance,
    log_high_performing_token,
    log_error,
    format_sol_amount,
    format_usd_amount,
    calculate_pnl
)

# OPTIMIZED: Removed unused Dict, Any imports and added performance monitoring
def monitor_performance(operation_name: str):
    """Decorator to monitor trade execution performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"{operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{operation_name} failed after {duration:.3f}s: {e}")
                raise
        return wrapper
    return decorator

logger = logging.getLogger(__name__)

class TradeExecutor:
    def __init__(self, config_manager, state_manager, pumpportal_trader: Optional[PumpPortalTrader] = None):
        self.config_manager = config_manager
        self.state_manager = state_manager
        self.pumpportal_trader = pumpportal_trader
        self.trading_settings = config_manager.get_trading_settings()
        self.notified_buy_event_ids = set()
        self.notified_sell_event_ids = set()

        # Dictionary to store event data for metadata tracking
        self.event_data = {}

        logger.info("TradeExecutor initialized with PumpPortal trader for real mode")

    # GMGN-specific methods removed - using PumpPortal trader for real mode

    @monitor_performance("BUY_EXECUTION")
    async def execute_buy(self, token_address: str, amount: float, price: float, slippage_bps: int, event_id: Optional[str] = None) -> dict:
        """Execute buy order via PumpPortal trader, preventing duplicate actions for the same event_id.

        Returns:
            dict: {'success': bool, 'tx_signature': str, 'data': dict, 'error': str}
        """
        try:
            if event_id:
                if event_id in self.notified_buy_event_ids:
                    logger.info(f"Buy action for event_id '{event_id}' (token: {token_address}) already processed. Skipping duplicate action.")
                    return True
            else:
                logger.warning(f"execute_buy called for {token_address} without an event_id. Duplicate action prevention may not be effective.")

            # Use PumpPortal trader for real buys
            if not self.pumpportal_trader:
                logger.error("Cannot execute real buy: PumpPortal trader not initialized.")
                return {'success': False, 'error': 'PumpPortal trader not initialized', 'tx_signature': '', 'data': {}}

            # Convert slippage from bps to percentage
            slippage_percent = slippage_bps / 100.0

            print(f"[REAL - PUMPPORTAL] Attempting to buy {format_sol_amount(amount)} SOL of {token_address} via PumpPortal. EventID: {event_id}")
            logger.info(f"Real buy: {amount:.4f} SOL of {token_address} with {slippage_percent:.1f}% slippage")

            # ENTERPRISE FIX: Enhanced unified trader execution with intelligent fallback
            try:
                    # Try unified trader first with comprehensive validation
                    if (hasattr(self, 'unified_trader') and self.unified_trader and
                        hasattr(self.unified_trader, 'get_current_trader_info')):

                        trader_info = self.unified_trader.get_current_trader_info()
                        logger.info(f"🔄 Using unified trader for buy execution (mode: {trader_info['network_mode']})")

                        # CRITICAL FIX: Extract market data from event metadata for adaptive slippage
                        market_data = {}
                        if event_id and event_id in self.event_data:
                            event_metadata = self.event_data[event_id].get('metadata', {})
                            market_data = {
                                'liquidity_usd': event_metadata.get('liquidity_usd', 0),
                                'volume_5m': event_metadata.get('volume_5m', 0),
                                'volume_24h': event_metadata.get('volume_24h', 0),
                                'market_cap': event_metadata.get('market_cap', 0)
                            }
                            logger.debug(f"Extracted market data for adaptive slippage: {market_data}")

                        result = await self.unified_trader.buy_token(
                            token_address=token_address.strip(),
                            sol_amount=amount,
                            slippage_percent=slippage_percent,
                            market_data=market_data
                        )

                        # Validate result before proceeding
                        if result and isinstance(result, dict) and 'success' in result:
                            success = result.get("success", False)
                            # If unified trader failed, fall back to direct PumpPortal
                            if not success and result.get("error_code") == "TRADER_NOT_AVAILABLE":
                                logger.warning("Unified trader not available, falling back to direct PumpPortal")
                                raise Exception("Unified trader not available - fallback to PumpPortal")
                        else:
                            logger.warning("Unified trader returned invalid result, falling back to PumpPortal")
                            raise Exception("Invalid unified trader result")

                    else:
                        # Graceful fallback to legacy PumpPortal (preserves existing functionality)
                        logger.info("🔄 Using PumpPortal trader for buy execution (legacy mode)")
                        result = await self.pumpportal_trader.buy_token(
                            token_address=token_address.strip(),
                            sol_amount=amount,
                            slippage_percent=slippage_percent
                        )
                        success = result.get("success", False)

                    if not success:
                        error_msg = result.get("error", "Unknown error")
                        error_code = result.get("error_code", "UNKNOWN")
                        logger.error(f"Buy failed: {error_msg} (Code: {error_code})")

                        # Handle specific error codes
                        if error_code == "INSUFFICIENT_BALANCE":
                            logger.error("Insufficient balance - check wallet SOL amount")
                        elif error_code == "NO_WALLET":
                            logger.error("No wallet configured - check .env file")
                    else:
                        tx_sig = result.get("data", {}).get("transaction_signature", "")
                        logger.info(f"Buy successful: {tx_sig}")

                    if success:
                        print(f"[REAL - PUMPPORTAL] ✅ Buy successful for {token_address}")
                        logger.info(f"PumpPortal buy successful for {token_address}")
                    else:
                        print(f"[REAL - PUMPPORTAL] ❌ Buy failed for {token_address}")
                        logger.error(f"PumpPortal buy failed for {token_address}")

            except Exception as buy_ex:
                logger.warning(f"Unified trader failed: {buy_ex}, falling back to direct PumpPortal")
                # Fallback to direct PumpPortal trader
                try:
                    logger.info("🔄 Fallback: Using direct PumpPortal trader for buy execution")
                    result = await self.pumpportal_trader.buy_token(
                        token_address=token_address.strip(),
                        sol_amount=amount,
                        slippage_percent=slippage_percent
                    )
                    success = result.get("success", False)

                    if success:
                        logger.info("✅ Fallback to direct PumpPortal successful")
                    else:
                        logger.error(f"❌ Fallback to direct PumpPortal also failed: {result.get('error', 'Unknown error')}")

                except Exception as fallback_ex:
                    logger.error(f"❌ Both unified trader and direct PumpPortal failed: {fallback_ex}")
                    success = False
                    result = {'success': False, 'error': f"Both traders failed: {str(buy_ex)}, {str(fallback_ex)}", 'tx_signature': '', 'data': {}}

            if success:
                log_trade("buy", token_address, amount, price)
                if event_id:
                    self.notified_buy_event_ids.add(event_id)
                    logger.info(f"Added {event_id} to notified_buy_event_ids. Current size: {len(self.notified_buy_event_ids)}")

                # Update the state manager with the new position
                try:
                    # Get metadata from the event_data if available
                    event_metadata = self.event_data.get(event_id, {}).get('metadata', {}) if event_id else {}

                    # Create metadata for the position
                    metadata = {
                        "source": event_metadata.get('source', 'trade_executor'),
                        "signal_source": event_metadata.get('signal_source', 'unknown'),
                        "channel_id": event_metadata.get('channel_id', 'unknown'),
                        "channel_name": event_metadata.get('channel_name', 'unknown'),
                        "is_gmgn_channel": event_metadata.get('is_gmgn_channel', False),
                        "detection_time": event_metadata.get('detection_time', 0),
                        "event_id": event_id,
                        "timestamp": time.time(),
                        "token_symbol": event_metadata.get('token_symbol', "Unknown"),
                        "token_name": event_metadata.get('token_name', "Unknown"),
                        "initial_confidence": event_metadata.get('initial_confidence', 0.0),
                        "entry_liquidity_usd": event_metadata.get('liquidity_usd', 0),
                        "entry_holder_count": event_metadata.get('holder_count', 0),
                        "entry_top10_percent": event_metadata.get('top10_percent', 0)
                    }

                    # CRITICAL FIX: Open the position in the state manager with await
                    await self.state_manager.open_position(
                        token=token_address,
                        entry_price=price,
                        sol_amount=amount,
                        strategy="default",  # Use default strategy
                        metadata=metadata
                    )

                    logger.info(f"Position opened in state manager for {token_address} with {amount:.4f} SOL at ${price:.8f}")

                    # BULLETPROOF: Mark token as bought for position monitoring
                    logger.info(f"✅ BOUGHT: {token_address} - position opened successfully with {amount:.4f} SOL")

                except Exception as state_error:
                    logger.error(f"Error updating state after buy for {token_address}: {state_error}")

                return {'success': True, 'verified': result.get('verified', True), 'tx_signature': result.get('data', {}).get('transaction_signature', ''), 'transaction_signature': result.get('data', {}).get('transaction_signature', ''), 'data': result, 'error': ''}
            else:
                 # Error already logged by PumpPortal trader or printed above
                log_error("buy_error", f"Failed PumpPortal buy command for {token_address}", token_address)
                return {'success': False, 'verified': False, 'error': result.get('error', 'Unknown buy error'), 'tx_signature': '', 'transaction_signature': '', 'data': result}

        except Exception as e:
            logger.error(f"Error in execute_buy for {token_address}: {e}", exc_info=True)
            log_error("buy_error", str(e), token_address)
            return {'success': False, 'verified': False, 'error': str(e), 'tx_signature': '', 'transaction_signature': '', 'data': {}}

    @monitor_performance("SELL_EXECUTION")
    async def execute_sell(self, token_address: str, token_amount: float, entry_price: float, slippage_bps: int, sell_fraction: float = 1.0, event_id: Optional[str] = None) -> dict:
        """Execute sell order (selling token_amount * sell_fraction) via PumpPortal trader, preventing duplicate actions for the same event_id.

        Returns:
            dict: {'success': bool, 'tx_signature': str, 'data': dict, 'error': str}
        """
        try:
            # CRITICAL DEBUG: Enhanced sell execution logging
            logger.info(f"🚨 TRADE EXECUTOR: execute_sell called for {token_address}")
            logger.info(f"📊 Token Amount: {token_amount}")
            logger.info(f"💰 Entry Price: {entry_price}")
            logger.info(f"📈 Sell Fraction: {sell_fraction}")
            logger.info(f"🔑 EventID: {event_id}")

            # CRITICAL: Log to console for immediate visibility
            print(f"\n🚨 TRADE EXECUTOR: execute_sell called for {token_address}")
            print(f"📊 Token Amount: {token_amount}")
            print(f"💰 Entry Price: {entry_price}")
            print(f"📈 Sell Fraction: {sell_fraction}")
            print(f"🔑 EventID: {event_id}\n")

            if event_id:
                if event_id in self.notified_sell_event_ids:
                    logger.info(f"Sell action for event_id '{event_id}' (token: {token_address}) already processed. Skipping duplicate action.")
                    print(f"⚠️ DUPLICATE SELL SKIPPED: {token_address} (EventID: {event_id})")
                    return {'success': True, 'tx_signature': '', 'data': {}, 'error': 'Duplicate action skipped'}
            else:
                logger.warning(f"execute_sell called for {token_address} without an event_id. Duplicate action prevention may not be effective.")
                print(f"⚠️ NO EVENT ID PROVIDED: {token_address}")

            amount_to_sell_tokens = token_amount * sell_fraction # Calculate actual token amount based on fraction
            logger.info(f"SELL DEBUG: Calculated amount_to_sell_tokens={amount_to_sell_tokens} (token_amount={token_amount}, sell_fraction={sell_fraction})")

            if amount_to_sell_tokens <= 1e-9: # Avoid selling dust
                logger.error(f"SELL FAILED: Sell amount for {token_address} is near zero ({amount_to_sell_tokens}). Token amount: {token_amount}, Sell fraction: {sell_fraction}")
                return {'success': False, 'error': 'Sell amount too small (dust)', 'tx_signature': '', 'data': {}}

            # Use PumpPortal trader for real sells
            if not self.pumpportal_trader:
                logger.error("Cannot execute real sell: PumpPortal trader not initialized.")
                return {'success': False, 'error': 'PumpPortal trader not initialized', 'tx_signature': '', 'data': {}}

            # Convert slippage from bps to percentage
            slippage_percent = slippage_bps / 100.0

            clean_token_address = token_address.strip()

            print(f"[REAL - PUMPPORTAL] Attempting to sell {amount_to_sell_tokens:.4f} tokens ({sell_fraction*100:.1f}%) of {clean_token_address} via PumpPortal.")
            logger.info(f"Real sell: {amount_to_sell_tokens:.4f} tokens of {clean_token_address} with {slippage_percent:.1f}% slippage")

            # ENTERPRISE FIX: Enhanced unified trader execution with intelligent fallback
            try:
                    # Try unified trader first with comprehensive validation
                    if (hasattr(self, 'unified_trader') and self.unified_trader and
                        hasattr(self.unified_trader, 'get_current_trader_info')):

                        trader_info = self.unified_trader.get_current_trader_info()
                        logger.info(f"🔄 Using unified trader for sell execution (mode: {trader_info['network_mode']})")

                        # CRITICAL FIX: Extract market data from event metadata for adaptive slippage
                        market_data = {}
                        if event_id and event_id in self.event_data:
                            event_metadata = self.event_data[event_id].get('metadata', {})
                            market_data = {
                                'liquidity_usd': event_metadata.get('liquidity_usd', 0),
                                'volume_5m': event_metadata.get('volume_5m', 0),
                                'volume_24h': event_metadata.get('volume_24h', 0),
                                'market_cap': event_metadata.get('market_cap', 0)
                            }
                            logger.debug(f"Extracted market data for adaptive slippage: {market_data}")

                        # CRITICAL DEBUG: Log before unified trader call
                        logger.info(f"🔄 CALLING UNIFIED TRADER: sell_token for {clean_token_address}")
                        logger.info(f"📊 Amount: {amount_to_sell_tokens} tokens")
                        logger.info(f"🎯 Slippage: {slippage_percent}%")
                        print(f"\n🔄 CALLING UNIFIED TRADER: sell_token for {clean_token_address}")
                        print(f"📊 Amount: {amount_to_sell_tokens} tokens")
                        print(f"🎯 Slippage: {slippage_percent}%\n")

                        result = await self.unified_trader.sell_token(
                            token_address=clean_token_address,
                            token_amount=amount_to_sell_tokens,
                            slippage_percent=slippage_percent,
                            market_data=market_data
                        )

                        # CRITICAL DEBUG: Log unified trader result
                        logger.info(f"📋 UNIFIED TRADER RESULT: {result}")
                        print(f"📋 UNIFIED TRADER RESULT: {result}")

                        # Validate result before proceeding
                        if result and isinstance(result, dict) and 'success' in result:
                            success = result.get("success", False)
                            # If unified trader failed, fall back to direct PumpPortal
                            if not success and result.get("error_code") == "TRADER_NOT_AVAILABLE":
                                logger.warning("Unified trader not available, falling back to direct PumpPortal")
                                raise Exception("Unified trader not available - fallback to PumpPortal")
                        else:
                            logger.warning("Unified trader returned invalid result, falling back to PumpPortal")
                            raise Exception("Invalid unified trader result")

                    else:
                        # Graceful fallback to legacy PumpPortal (preserves existing functionality)
                        logger.info("🔄 Using PumpPortal trader for sell execution (legacy mode)")
                        result = await self.pumpportal_trader.sell_token(
                            token_address=clean_token_address,
                            token_amount=amount_to_sell_tokens,
                            slippage_percent=slippage_percent
                        )
                        success = result.get("success", False)

                    if not success:
                        error_msg = result.get("error", "Unknown error")
                        error_code = result.get("error_code", "UNKNOWN")
                        logger.error(f"Sell failed: {error_msg} (Code: {error_code})")

                        # Handle specific error codes
                        if error_code == "TRADE_EXECUTION_FAILED":
                            retry_after = result.get("retry_after", 5)
                            logger.warning(f"Trade execution failed - retry after {retry_after}s")
                        elif error_code == "NO_WALLET":
                            logger.error("No wallet configured - check .env file")
                    else:
                        tx_sig = result.get("data", {}).get("transaction_signature", "")
                        logger.info(f"Sell successful: {tx_sig}")

                    if success:
                        print(f"[REAL - PUMPPORTAL] ✅ Sell successful for {clean_token_address}")
                        logger.info(f"PumpPortal sell successful for {clean_token_address}")
                    else:
                        print(f"[REAL - PUMPPORTAL] ❌ Sell failed for {clean_token_address}")
                        logger.error(f"PumpPortal sell failed for {clean_token_address}")

            except Exception as sell_ex:
                logger.warning(f"Unified trader failed: {sell_ex}, falling back to direct PumpPortal")
                # Fallback to direct PumpPortal trader
                try:
                    logger.info("🔄 Fallback: Using direct PumpPortal trader for sell execution")
                    result = await self.pumpportal_trader.sell_token(
                        token_address=clean_token_address,
                        token_amount=amount_to_sell_tokens,
                        slippage_percent=slippage_percent
                    )
                    success = result.get("success", False)

                    if success:
                        logger.info("✅ Fallback to direct PumpPortal successful")
                    else:
                        logger.error(f"❌ Fallback to direct PumpPortal also failed: {result.get('error', 'Unknown error')}")

                except Exception as fallback_ex:
                    logger.error(f"❌ Both unified trader and direct PumpPortal failed: {fallback_ex}")
                    success = False
                    result = {'success': False, 'error': f"Both traders failed: {str(sell_ex)}, {str(fallback_ex)}", 'tx_signature': '', 'data': {}}

            if success:
                # Get current market price for proper logging
                try:
                    current_price = 0.0
                    if hasattr(self, 'token_analyzer') and self.token_analyzer:
                        # Try to get current price from token analyzer
                        analysis = await self.token_analyzer.analyze(token_address, force_fresh=True)
                        if analysis and analysis.get('exists', False):
                            current_price = analysis.get('price', 0.0)

                    # If we couldn't get current price, use entry price as fallback
                    if current_price <= 0:
                        current_price = entry_price
                        logger.warning(f"Could not get current price for {token_address}, using entry price {entry_price}")

                    # Calculate estimated PnL for logging
                    estimated_pnl_percent = ((current_price - entry_price) / entry_price * 100) if entry_price > 0 else 0

                    logger.info(f"SELL PRICE LOGGING: {token_address} - Entry: {entry_price:.8f}, Current: {current_price:.8f}, Est. PnL: {estimated_pnl_percent:.2f}%")

                except Exception as e:
                    logger.error(f"Error getting current price for sell logging: {e}")
                    current_price = entry_price
                    estimated_pnl_percent = 0

                # Log trade attempt with actual price data
                log_trade("sell_attempt", token_address, amount_to_sell_tokens, current_price, estimated_pnl_percent, details=f"Fraction: {sell_fraction*100:.1f}%")
                if event_id:
                    self.notified_sell_event_ids.add(event_id)
                    logger.info(f"SELL DEBUG: Added {event_id} to notified_sell_event_ids. Current size: {len(self.notified_sell_event_ids)}")

                # BULLETPROOF: Remove from position monitoring if full sell
                if sell_fraction >= 1.0:
                    try:
                        # Close position in state manager directly
                        if hasattr(self, 'state_manager') and self.state_manager:
                            await self.state_manager.close_position(
                                token=token_address,
                                exit_price=current_price,
                                reason=f"Full sell executed (fraction: {sell_fraction})",
                                fraction=sell_fraction
                            )
                            logger.info(f"💰 SOLD: {token_address} - position closed in StateManager")
                        else:
                            logger.warning(f"⚠️ StateManager not available to close position for {token_address}")
                    except Exception as e:
                        logger.error(f"❌ Error closing position in StateManager: {e}")

                # CRITICAL FIX: Ensure proper dict return format
                sell_result = {
                    'success': True,
                    'verified': result.get('verified', True),  # Use verification from PumpPortal result
                    'tx_signature': result.get('data', {}).get('transaction_signature', ''),
                    'transaction_signature': result.get('data', {}).get('transaction_signature', ''),
                    'data': result,
                    'error': ''
                }
                logger.info(f"✅ SELL SUCCESS: Returning proper dict format for {token_address}")
                return sell_result
            else:
                # Error already logged by PumpPortal trader or printed above
                log_error("sell_error", f"Failed PumpPortal sell command for {token_address}", token_address)
                sell_error_result = {
                    'success': False,
                    'verified': False,  # Failed sells are not verified
                    'error': result.get('error', 'Unknown sell error'),
                    'tx_signature': '',
                    'transaction_signature': '',
                    'data': result
                }
                logger.error(f"❌ SELL FAILED: Returning proper dict format for {token_address}")
                return sell_error_result

        except Exception as e:
            logger.error(f"Error in execute_sell for {token_address}: {e}", exc_info=True)
            log_error("sell_error", str(e), token_address)
            return {'success': False, 'verified': False, 'error': str(e), 'tx_signature': '', 'transaction_signature': '', 'data': {}}

    def create_moonbag(self, token_address: str, amount: float, price: float) -> bool:
        """Create moonbag position"""
        try:
            # Implement real moonbag creation logic here
            print(f"[REAL] Creating moonbag of {format_sol_amount(amount)} at {format_usd_amount(price)}")

            log_trade("moonbag_create", token_address, amount, price)
            return True

        except Exception as e:
            log_error("moonbag_error", str(e), token_address)
            return False

    def sell_moonbag(self, token_address: str, amount: float, price: float, entry_price: float) -> bool:
        """Sell moonbag position"""
        try:
            pnl = calculate_pnl(entry_price, price, amount)

            # Implement real moonbag sell logic here
            print(f"[REAL] Selling moonbag of {format_sol_amount(amount)} at {format_usd_amount(price)} (PNL: {format_usd_amount(pnl)})")

            log_trade("moonbag_sell", token_address, amount, price, pnl)
            log_performance(token_address, entry_price, price, pnl, 0)  # Duration will be updated later

            if pnl > 0:
                log_high_performing_token(token_address, pnl, amount * price)

            return True

        except Exception as e:
            log_error("moonbag_sell_error", str(e), token_address)
            return False