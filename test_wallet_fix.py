#!/usr/bin/env python3
"""
Test the wallet loading fix
"""

import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_wallet_fix():
    """Test if wallet loading is now working correctly"""
    try:
        print("🔧 TESTING WALLET LOADING FIX")
        print("=" * 40)
        
        # Import required modules
        from config_manager import ConfigManager
        from pumpportal_trader import PumpPortalTrader
        
        print("✅ Modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        
        # Initialize PumpPortal trader
        trader = PumpPortalTrader(config)
        
        print("✅ PumpPortal trader initialized")
        
        # Check wallet loading
        print(f"\n💼 WALLET ANALYSIS")
        print("=" * 20)
        
        if trader.default_wallet:
            wallet = trader.default_wallet
            print(f"✅ Wallet loaded successfully!")
            print(f"🔑 Name: {wallet.get('name', 'Unknown')}")
            print(f"📍 Public Key: {wallet.get('public_key', 'Not available')}")
            print(f"🔐 Has Private Key: {'Yes' if wallet.get('private_key') else 'No'}")
            
            # Test if we can derive public key
            if wallet.get('public_key'):
                print(f"✅ Public key is available: {wallet['public_key']}")
                
                # Test wallet balance check
                try:
                    print(f"\n💰 TESTING WALLET BALANCE CHECK")
                    print("=" * 35)
                    
                    balance = await trader.check_wallet_balance()
                    print(f"💰 Wallet balance: {balance:.6f} SOL")
                    
                    if balance > 0:
                        print("✅ Wallet has SOL balance - ready for trading!")
                    else:
                        print("⚠️ Wallet has no SOL balance")
                        
                except Exception as e:
                    print(f"❌ Error checking wallet balance: {e}")
            else:
                print("❌ Public key not available")
                
        else:
            print("❌ No wallet loaded")
            return False
        
        # Test transaction capability
        print(f"\n🔍 TRANSACTION CAPABILITY TEST")
        print("=" * 35)
        
        # Check if we can create a keypair (needed for transactions)
        try:
            import base58
            from solders.keypair import Keypair
            
            private_key = wallet.get('private_key')
            if private_key:
                private_key_bytes = base58.b58decode(private_key)
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)
                
                derived_public_key = str(keypair.pubkey())
                stored_public_key = wallet.get('public_key')
                
                if derived_public_key == stored_public_key:
                    print("✅ Keypair derivation working correctly")
                    print("✅ Wallet is ready for real transactions")
                else:
                    print(f"❌ Public key mismatch!")
                    print(f"   Derived: {derived_public_key}")
                    print(f"   Stored: {stored_public_key}")
                    
        except Exception as e:
            print(f"❌ Error testing keypair derivation: {e}")
        
        print(f"\n🎯 FINAL VERDICT")
        print("=" * 20)
        
        if (trader.default_wallet and 
            trader.default_wallet.get('public_key') and 
            trader.default_wallet.get('private_key')):
            print("✅ WALLET FIX SUCCESSFUL!")
            print("✅ Bot can now execute REAL transactions")
            print("✅ No more fake transaction signatures")
            return True
        else:
            print("❌ WALLET FIX FAILED!")
            print("❌ Bot still cannot execute real transactions")
            return False
        
    except Exception as e:
        print(f"❌ Error in wallet test: {e}")
        logger.error(f"Wallet test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = asyncio.run(test_wallet_fix())
    if result:
        print("\n🎉 WALLET FIX VERIFIED!")
    else:
        print("\n💥 WALLET FIX FAILED!")
