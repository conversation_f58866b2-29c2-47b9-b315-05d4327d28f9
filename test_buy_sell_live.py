#!/usr/bin/env python3
"""
LIVE BUY/SELL TEST
This script will perform a real buy and sell test with 0.001 SOL to verify functionality
"""

import asyncio
import logging
import time
import json
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_buy_sell_live():
    """Test live buy and sell functionality with 0.001 SOL"""
    try:
        print("🚨 LIVE BUY/SELL TEST STARTING")
        print("=" * 50)
        print("⚠️  WARNING: This will use REAL SOL on MAINNET")
        print("💰 Amount: 0.001 SOL")
        print("⏰ Max hold time: 1 minute")
        print("=" * 50)
        
        # Import required modules
        from config_manager import ConfigManager
        from state_manager import StateManager
        from trade_executor import TradeExecutor
        from execution_queue import TradeExecutionQueue
        from bot_controller import BotController
        from token_analyzer import TokenAnalyzer
        
        print("✅ All modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        state = StateManager(config)
        
        # Verify test configuration
        trading_settings = config.get_section('trading_settings')
        sol_amount = trading_settings.get('sol_trade_amount', 0.001)
        max_hold_time = trading_settings.get('max_hold_time_minutes', 1)
        
        print(f"✅ Configuration verified:")
        print(f"   💰 SOL amount: {sol_amount}")
        print(f"   ⏰ Max hold time: {max_hold_time} minutes")
        
        if sol_amount != 0.001:
            print(f"❌ ERROR: Expected 0.001 SOL, got {sol_amount}")
            return False
        
        # Initialize bot controller
        bot_controller = BotController(config, state)
        await bot_controller.initialize(enable_signal_processing=False)
        
        print("✅ Bot controller initialized")
        
        # Check wallet balance
        if hasattr(bot_controller, 'trade_executor') and bot_controller.trade_executor:
            try:
                # Get wallet info
                wallet_info = await bot_controller.trade_executor.get_wallet_info()
                if wallet_info and 'balance' in wallet_info:
                    balance = wallet_info['balance']
                    print(f"💰 Wallet balance: {balance:.6f} SOL")
                    
                    if balance < 0.01:  # Need at least 0.01 SOL for test + fees
                        print(f"❌ ERROR: Insufficient balance. Need at least 0.01 SOL, have {balance:.6f}")
                        return False
                else:
                    print("⚠️  Could not verify wallet balance")
            except Exception as e:
                print(f"⚠️  Could not check wallet balance: {e}")
        
        # Find a suitable token to test with
        print("\n🔍 FINDING SUITABLE TOKEN FOR TEST")
        print("=" * 40)
        
        # Use a known active token for testing (we'll find one from DexScreener)
        token_analyzer = TokenAnalyzer(config)
        
        # Get some trending tokens from pump.fun
        test_tokens = [
            "8Q8KPBL21FVatn2C1EaxAQSorAkHW2W2jDUXapVPZmhm",  # Current position
            "So11111111111111111111111111111111111111112",   # Wrapped SOL (safe test)
        ]
        
        selected_token = None
        for token in test_tokens:
            try:
                print(f"🔍 Analyzing {token}...")
                analysis = await token_analyzer.analyze(token, force_fresh=True)
                
                if analysis and analysis.get('price', 0) > 0:
                    liquidity = analysis.get('liquidity_usd', 0)
                    print(f"   💰 Price: ${analysis.get('price', 0):.8f}")
                    print(f"   🌊 Liquidity: ${liquidity:.2f}")
                    
                    if liquidity > 1000:  # Minimum liquidity for safe test
                        selected_token = token
                        print(f"✅ Selected token: {token}")
                        break
                    else:
                        print(f"   ❌ Insufficient liquidity: ${liquidity:.2f}")
                else:
                    print(f"   ❌ Could not analyze token")
                    
            except Exception as e:
                print(f"   ❌ Error analyzing {token}: {e}")
        
        if not selected_token:
            print("❌ ERROR: Could not find suitable token for testing")
            return False
        
        # STEP 1: BUY TEST
        print(f"\n🛒 STEP 1: BUY TEST")
        print("=" * 30)
        print(f"🎯 Token: {selected_token}")
        print(f"💰 Amount: {sol_amount} SOL")
        
        # Record initial state
        initial_positions = state.get_open_positions()
        initial_position_count = len(initial_positions)
        print(f"📊 Initial positions: {initial_position_count}")
        
        # Execute buy
        buy_start_time = time.time()
        try:
            buy_result = await bot_controller.execution_queue.queue_buy(
                token_address=selected_token,
                amount=sol_amount,
                price=analysis.get('price', 0),
                slippage_bps=1500,  # 15% slippage for test
                priority=1,
                event_id=f"test_buy_{int(buy_start_time)}"
            )
            
            if buy_result:
                print("✅ Buy order queued successfully")
                
                # Wait for buy to complete
                print("⏳ Waiting for buy execution...")
                for i in range(30):  # Wait up to 30 seconds
                    await asyncio.sleep(1)
                    current_positions = state.get_open_positions()
                    
                    if len(current_positions) > initial_position_count:
                        print(f"✅ BUY SUCCESSFUL! Position opened.")
                        print(f"⏱️  Buy execution time: {time.time() - buy_start_time:.2f} seconds")
                        
                        # Show position details
                        new_position = None
                        for token, position in current_positions.items():
                            if token not in initial_positions:
                                new_position = position
                                print(f"📊 Position details:")
                                print(f"   🎯 Token: {token}")
                                print(f"   💰 Amount: {position.get('token_amount', 0):.6f} tokens")
                                print(f"   💵 Entry price: ${position.get('entry_price', 0):.8f}")
                                print(f"   📅 Purchase time: {datetime.fromtimestamp(position.get('purchase_time', 0))}")
                                break
                        
                        break
                    
                    print(f"   ⏳ Waiting... ({i+1}/30)")
                
                else:
                    print("❌ BUY FAILED: Position not created within 30 seconds")
                    return False
                    
            else:
                print("❌ BUY FAILED: Could not queue buy order")
                return False
                
        except Exception as e:
            print(f"❌ BUY ERROR: {e}")
            return False
        
        # STEP 2: WAIT AND MONITOR
        print(f"\n⏳ STEP 2: MONITORING POSITION")
        print("=" * 35)
        print(f"⏰ Max hold time: {max_hold_time} minute(s)")
        print("🔍 Monitoring for sell triggers...")
        
        # Start position monitoring if not already running
        if not hasattr(bot_controller, '_monitor_task') or bot_controller._monitor_task is None or bot_controller._monitor_task.done():
            await bot_controller._start_position_monitoring_resilient()
            print("✅ Position monitoring started")
        
        # Monitor position for sell triggers
        sell_triggered = False
        monitor_start_time = time.time()
        max_monitor_time = (max_hold_time * 60) + 30  # Max hold time + 30 seconds buffer
        
        while time.time() - monitor_start_time < max_monitor_time:
            current_positions = state.get_open_positions()
            
            if len(current_positions) <= initial_position_count:
                print("✅ SELL TRIGGERED! Position closed automatically.")
                sell_triggered = True
                break
            
            # Show monitoring status
            elapsed_minutes = (time.time() - buy_start_time) / 60
            print(f"   📊 Monitoring... {elapsed_minutes:.1f}m elapsed | Positions: {len(current_positions)}")
            
            await asyncio.sleep(5)  # Check every 5 seconds
        
        # STEP 3: FORCE SELL IF NEEDED
        if not sell_triggered:
            print(f"\n🔥 STEP 3: FORCE SELL (MAX TIME EXCEEDED)")
            print("=" * 45)
            
            current_positions = state.get_open_positions()
            if len(current_positions) > initial_position_count:
                # Find our test position
                for token, position in current_positions.items():
                    if token not in initial_positions:
                        print(f"🔥 Force selling position: {token}")
                        
                        try:
                            force_sell_result = await bot_controller.execution_queue.queue_sell(
                                token_address=token,
                                token_amount=position.get('token_amount', 0),
                                entry_price=position.get('entry_price', 0),
                                slippage_bps=1500,  # 15% slippage
                                sell_fraction=1.0,  # Sell 100%
                                priority=0,  # Emergency priority
                                event_id=f"test_force_sell_{int(time.time())}"
                            )
                            
                            if force_sell_result:
                                print("✅ Force sell queued successfully")
                                
                                # Wait for sell to complete
                                print("⏳ Waiting for sell execution...")
                                for i in range(30):  # Wait up to 30 seconds
                                    await asyncio.sleep(1)
                                    updated_positions = state.get_open_positions()
                                    
                                    if token not in updated_positions:
                                        print("✅ FORCE SELL SUCCESSFUL! Position closed.")
                                        sell_triggered = True
                                        break
                                    
                                    print(f"   ⏳ Waiting... ({i+1}/30)")
                                
                                if not sell_triggered:
                                    print("❌ FORCE SELL FAILED: Position still open after 30 seconds")
                            else:
                                print("❌ FORCE SELL FAILED: Could not queue sell order")
                                
                        except Exception as e:
                            print(f"❌ FORCE SELL ERROR: {e}")
                        
                        break
        
        # FINAL RESULTS
        print(f"\n🎯 FINAL RESULTS")
        print("=" * 20)
        
        final_positions = state.get_open_positions()
        final_position_count = len(final_positions)
        
        print(f"📊 Initial positions: {initial_position_count}")
        print(f"📊 Final positions: {final_position_count}")
        
        if sell_triggered and final_position_count == initial_position_count:
            print("✅ TEST SUCCESSFUL!")
            print("   ✅ Buy executed successfully")
            print("   ✅ Sell executed successfully")
            print("   ✅ Position properly closed")
            return True
        else:
            print("❌ TEST FAILED!")
            if final_position_count > initial_position_count:
                print("   ❌ Position still open - sell failed")
            return False
        
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        logger.error(f"Live test failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = asyncio.run(test_buy_sell_live())
    if result:
        print("\n🎉 LIVE TEST COMPLETED SUCCESSFULLY!")
    else:
        print("\n💥 LIVE TEST FAILED!")
