#!/usr/bin/env python3
"""
Manual sell script for remaining tokens
"""

import asyncio
import logging
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def manual_sell_remaining_tokens():
    """Manually sell any remaining tokens"""
    try:
        print("🔥 MANUAL SELL FOR REMAINING TOKENS")
        print("=" * 45)
        
        # Import required modules
        from config_manager import ConfigManager
        from state_manager import StateManager
        from trade_executor import TradeExecutor
        from execution_queue import TradeExecutionQueue
        from bot_controller import BotController
        
        print("✅ Modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        state = StateManager(config)
        
        # Initialize bot controller
        bot_controller = BotController(config, state)
        await bot_controller.initialize(enable_signal_processing=False)
        
        print("✅ Bot controller initialized")
        
        # The problematic token
        problem_token = "3LuFLwgefb6DnBBKJj3wQrtENgj21YzSF2LJMLtVpump"
        
        print(f"\n🎯 Target token: {problem_token}")
        print("=" * 60)
        
        # Check execution queue
        execution_queue = bot_controller.execution_queue
        print(f"🔄 Execution queue running: {execution_queue.is_running}")
        
        if not execution_queue.is_running:
            print("🚨 Starting execution queue...")
            await execution_queue.start()
            print("✅ Execution queue started")
        
        # Try to sell the token manually
        print(f"\n🔥 ATTEMPTING MANUAL SELL")
        print("=" * 30)
        
        # Estimate token amount (from the logs, it was 3.6724201248622843)
        estimated_token_amount = 3.6724201248622843
        estimated_entry_price = 0.0002519  # From the logs
        
        print(f"📊 Estimated token amount: {estimated_token_amount}")
        print(f"💰 Estimated entry price: ${estimated_entry_price}")
        
        try:
            # Queue emergency sell with high priority
            sell_result = await execution_queue.queue_sell(
                token_address=problem_token,
                token_amount=estimated_token_amount,
                entry_price=estimated_entry_price,
                slippage_bps=2000,  # 20% slippage for emergency sell
                sell_fraction=1.0,  # Sell 100%
                priority=0,  # Emergency priority
                event_id=f"manual_emergency_sell_{int(time.time())}"
            )
            
            if sell_result:
                print("✅ Emergency sell queued successfully")
                
                # Wait for sell to complete
                print("⏳ Waiting for sell execution...")
                for i in range(60):  # Wait up to 60 seconds
                    await asyncio.sleep(1)
                    
                    # Check if there are any open positions with this token
                    current_positions = state.get_open_positions()
                    if problem_token not in current_positions:
                        print(f"✅ MANUAL SELL SUCCESSFUL! Token no longer in positions.")
                        break
                    
                    print(f"   ⏳ Waiting... ({i+1}/60)")
                
                else:
                    print("⚠️ Sell may still be processing or failed")
                    
            else:
                print("❌ Failed to queue emergency sell")
                
        except Exception as e:
            print(f"❌ Error during manual sell: {e}")
            logger.error(f"Manual sell failed: {e}", exc_info=True)
        
        # Alternative: Try direct trader sell
        print(f"\n🔄 ALTERNATIVE: DIRECT TRADER SELL")
        print("=" * 40)
        
        try:
            # Get the unified trader
            unified_trader = bot_controller.trade_executor.unified_trader
            
            print("🔥 Attempting direct sell via unified trader...")
            
            # Try to sell directly
            direct_sell_result = await unified_trader.sell_token(
                token_address=problem_token,
                token_amount=estimated_token_amount,
                slippage_percent=20.0  # 20% slippage
            )
            
            print(f"📋 Direct sell result: {direct_sell_result}")
            
            if direct_sell_result.get('success', False):
                tx_signature = direct_sell_result.get('data', {}).get('transaction_signature', 'N/A')
                print(f"✅ DIRECT SELL SUCCESSFUL!")
                print(f"🔗 Transaction: https://solscan.io/tx/{tx_signature}")
            else:
                error = direct_sell_result.get('error', 'Unknown error')
                print(f"❌ Direct sell failed: {error}")
                
        except Exception as e:
            print(f"❌ Error during direct sell: {e}")
            logger.error(f"Direct sell failed: {e}", exc_info=True)
        
        # Final recommendations
        print(f"\n💡 FINAL RECOMMENDATIONS")
        print("=" * 30)
        print("1. Check your wallet manually to see if tokens are still there")
        print("2. If tokens remain, try selling on Jupiter or Raydium DEX")
        print("3. The original transaction may have partially failed")
        print("4. Check the transaction on Solscan for details")
        print(f"5. Original TX: https://solscan.io/tx/2qXfpkme2Pke87wG7cbfapGfXkGNVK6n92pGoDCGEP6ksCpi89ASLsdfmCwrS1gukR5tpNZXeYXs4akZdjkM2ULf")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in manual sell: {e}")
        logger.error(f"Manual sell failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = asyncio.run(manual_sell_remaining_tokens())
    if result:
        print("\n✅ MANUAL SELL ATTEMPT COMPLETED")
    else:
        print("\n❌ MANUAL SELL ATTEMPT FAILED")
