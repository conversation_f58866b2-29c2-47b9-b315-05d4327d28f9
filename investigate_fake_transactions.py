#!/usr/bin/env python3
"""
CRITICAL INVESTIGATION: Why are transactions showing as confirmed but tokens still in wallet?
This could indicate fake transactions, simulation mode, or failed executions.
"""

import asyncio
import logging
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def investigate_fake_transactions():
    """Investigate why transactions appear confirmed but tokens remain in wallet"""
    try:
        print("🚨 CRITICAL INVESTIGATION: FAKE TRANSACTION ANALYSIS")
        print("=" * 65)
        
        # Import required modules
        from config_manager import ConfigManager
        from pumpportal_trader import PumpPortalTrader
        
        print("✅ Modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        
        print("🔍 CHECKING CONFIGURATION")
        print("=" * 30)
        
        # Check if we're in simulation mode
        trading_settings = config.get_section('trading_settings')
        mode = trading_settings.get('mode', 'unknown')
        print(f"🎯 Trading Mode: {mode}")
        
        if mode.lower() in ['simulation', 'test', 'demo', 'paper']:
            print("🚨 FOUND THE PROBLEM: BOT IS IN SIMULATION MODE!")
            print("Transactions are being simulated, not executed on real blockchain")
            return False
        
        # Check wallet configuration
        wallet_settings = config.get_section('wallet_settings')
        print(f"💼 Wallet Settings: {wallet_settings}")
        
        # Initialize trader to check actual configuration
        trader = PumpPortalTrader(config)
        print("✅ PumpPortal trader initialized")
        
        # Check if trader is in test mode
        print(f"\n🔍 TRADER CONFIGURATION ANALYSIS")
        print("=" * 40)
        
        # Check API URL
        if hasattr(trader, 'api_url'):
            api_url = trader.api_url
            print(f"🌐 API URL: {api_url}")
            
            if 'test' in api_url.lower() or 'demo' in api_url.lower() or 'simulation' in api_url.lower():
                print("🚨 FOUND THE PROBLEM: USING TEST/DEMO API!")
                print("Transactions are being sent to test environment, not mainnet")
                return False
        
        # Check RPC URL
        if hasattr(trader, 'rpc_url'):
            rpc_url = trader.rpc_url
            print(f"🔗 RPC URL: {rpc_url}")
            
            if 'test' in rpc_url.lower() or 'devnet' in rpc_url.lower():
                print("🚨 FOUND THE PROBLEM: USING TEST/DEVNET RPC!")
                print("Transactions are being sent to test network, not mainnet")
                return False
        
        # Check wallet details
        print(f"\n💼 WALLET ANALYSIS")
        print("=" * 20)
        
        if hasattr(trader, 'default_wallet'):
            wallet = trader.default_wallet
            print(f"🔑 Wallet Type: {type(wallet)}")
            print(f"📋 Wallet Data: {wallet}")
            
            # Try to get wallet address
            try:
                if isinstance(wallet, dict):
                    wallet_address = wallet.get('public_key') or wallet.get('address') or wallet.get('pubkey')
                else:
                    wallet_address = str(wallet)
                
                print(f"📍 Wallet Address: {wallet_address}")
                
                if not wallet_address or wallet_address == 'None':
                    print("🚨 FOUND THE PROBLEM: NO VALID WALLET ADDRESS!")
                    print("Transactions cannot be executed without a valid wallet")
                    return False
                    
            except Exception as e:
                print(f"❌ Error getting wallet address: {e}")
        
        # Check recent transaction details
        print(f"\n🔍 TRANSACTION VERIFICATION")
        print("=" * 30)
        
        recent_transactions = [
            "2qXfpkme2Pke87wG7cbfapGfXkGNVK6n92pGoDCGEP6ksCpi89ASLsdfmCwrS1gukR5tpNZXeYXs4akZdjkM2ULf",
            "543m61wC8jw6J5VzUri7dP6YQWcZdu9Nex9jnnwumF6NL4bZS3ay3er7ZKbWxdxPu8dpReBhQXq4Sn4JZwAYP1RN"
        ]
        
        for i, tx_sig in enumerate(recent_transactions, 1):
            print(f"\n🎯 Transaction {i}: {tx_sig}")
            print(f"🔗 Solscan: https://solscan.io/tx/{tx_sig}")
            
            # Try to verify transaction on blockchain
            try:
                if hasattr(trader, 'rpc_client'):
                    print("🔍 Verifying on blockchain...")
                    
                    # Get transaction details
                    tx_response = await trader.rpc_client.get_transaction(
                        tx_sig,
                        {"encoding": "jsonParsed", "maxSupportedTransactionVersion": 0}
                    )
                    
                    if tx_response and 'result' in tx_response:
                        tx_data = tx_response['result']
                        
                        if tx_data is None:
                            print("🚨 TRANSACTION NOT FOUND ON BLOCKCHAIN!")
                            print("This transaction may be fake or from a different network")
                        else:
                            meta = tx_data.get('meta', {})
                            error = meta.get('err')
                            
                            if error:
                                print(f"🚨 TRANSACTION FAILED: {error}")
                            else:
                                print("✅ Transaction exists and succeeded on blockchain")
                                
                                # Check if it actually moved tokens
                                pre_balances = meta.get('preTokenBalances', [])
                                post_balances = meta.get('postTokenBalances', [])
                                
                                print(f"📊 Token balance changes:")
                                print(f"   Pre: {len(pre_balances)} token accounts")
                                print(f"   Post: {len(post_balances)} token accounts")
                                
                                # Look for actual token movements
                                token_moved = False
                                for pre_bal in pre_balances:
                                    for post_bal in post_balances:
                                        if (pre_bal.get('accountIndex') == post_bal.get('accountIndex') and
                                            pre_bal.get('mint') == post_bal.get('mint')):
                                            
                                            pre_amount = float(pre_bal.get('uiTokenAmount', {}).get('uiAmount', 0))
                                            post_amount = float(post_bal.get('uiTokenAmount', {}).get('uiAmount', 0))
                                            
                                            if pre_amount != post_amount:
                                                print(f"   🔄 {pre_bal.get('mint')}: {pre_amount} → {post_amount}")
                                                token_moved = True
                                
                                if not token_moved:
                                    print("🚨 NO TOKEN MOVEMENTS DETECTED!")
                                    print("Transaction may have failed or been a no-op")
                    else:
                        print("❌ Could not fetch transaction from RPC")
                        
            except Exception as e:
                print(f"❌ Error verifying transaction: {e}")
        
        # Check if PumpPortal is actually executing trades
        print(f"\n🔍 PUMPPORTAL EXECUTION CHECK")
        print("=" * 35)
        
        # Look for any test/simulation flags in the trader
        trader_dict = vars(trader)
        suspicious_flags = []
        
        for key, value in trader_dict.items():
            if any(word in str(key).lower() for word in ['test', 'demo', 'simulation', 'fake', 'mock']):
                suspicious_flags.append(f"{key}: {value}")
            if any(word in str(value).lower() for word in ['test', 'demo', 'simulation', 'fake', 'mock']):
                suspicious_flags.append(f"{key}: {value}")
        
        if suspicious_flags:
            print("🚨 SUSPICIOUS FLAGS FOUND:")
            for flag in suspicious_flags:
                print(f"   ⚠️ {flag}")
        else:
            print("✅ No obvious test/simulation flags found")
        
        # Final analysis
        print(f"\n💡 INVESTIGATION RESULTS")
        print("=" * 25)
        print("Possible causes for fake confirmations:")
        print("1. Bot is in simulation/test mode")
        print("2. Using test/devnet RPC instead of mainnet")
        print("3. PumpPortal API is in test mode")
        print("4. Wallet configuration is invalid")
        print("5. Transactions are being simulated locally")
        print("6. Network/RPC issues causing false confirmations")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in investigation: {e}")
        logger.error(f"Investigation failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    result = asyncio.run(investigate_fake_transactions())
    if result:
        print("\n🔍 INVESTIGATION COMPLETED")
    else:
        print("\n🚨 CRITICAL ISSUE IDENTIFIED")
