2025-06-08 19:15:36,011 - __main__ - INFO - Successfully configured console output with UTF-8 encoding
2025-06-08 19:15:36,051 - __main__ - INFO - Starting bot...
2025-06-08 19:15:36,063 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 10.0 RPS
2025-06-08 19:15:36,063 - __main__ - INFO - [SUCCESS] Simple pump analyzer functions initialized successfully
2025-06-08 19:15:36,093 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:15:36,101 - state_manager - INFO - No existing state file found at sessions/trading_sim_session.session. Starting with fresh state.
2025-06-08 19:15:36,103 - state_manager - INFO - Initialized starting SOL from trading_settings.total_sol_capital: 1.5
2025-06-08 19:15:36,109 - pumpportal_trader - INFO - Loaded transaction settings: {'slippage_percent': 1.0, 'buy_tip_sol': 0.005, 'handling_fee_percent': 1.0, 'gas_price_sol': 0.005, 'platform_fee_percent': 1, 'use_adaptive_slippage': True}
2025-06-08 19:15:36,110 - pumpportal_trader - WARNING - No wallet found in .env file
2025-06-08 19:15:36,111 - pumpportal_trader - INFO - PumpPortal Trader initialized for real mode
2025-06-08 19:15:36,111 - pumpportal_trader - INFO - API URL: https://pumpportal.fun/api/trade-local
2025-06-08 19:15:36,111 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-08 19:15:36,112 - pumpportal_trader - INFO - Helius rate limiter: 10.0 RPS
2025-06-08 19:15:36,112 - trade_executor - INFO - TradeExecutor initialized with PumpPortal trader for real mode
2025-06-08 19:15:36,114 - __main__ - INFO - [SUCCESS] Fast analysis function connected to bot controller
2025-06-08 19:15:36,115 - __main__ - INFO - Initializing DEX adapters and sessions
2025-06-08 19:15:36,122 - __main__ - INFO - DEX sessions initialized successfully
2025-06-08 19:15:36,122 - __main__ - INFO - LLM integration is not available in this configuration
2025-06-08 19:15:36,123 - __main__ - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-08 19:15:36,123 - __main__ - INFO - LLM integration is not available in this configuration
2025-06-08 19:15:36,124 - __main__ - INFO - Initializing bot controller with position monitoring...
2025-06-08 19:15:36,125 - __main__ - INFO - [SUCCESS] Bot controller initialized successfully with position monitoring
2025-06-08 19:15:36,125 - __main__ - INFO - Started cache cleanup task
2025-06-08 19:15:36,125 - __main__ - INFO - Starting CLI interface...
2025-06-08 19:16:04,050 - state_manager - INFO - State saved successfully to sessions/trading_sim_session.session
2025-06-08 19:16:05,191 - simulation_logger - INFO - Simulation logger initialized. Trades file: simulation_logs\sim_trades_20250608_191605.csv
2025-06-08 19:16:05,194 - execution_queue - INFO - Starting execution queue with 4 workers
2025-06-08 19:16:05,195 - execution_queue - INFO - Started execution worker 0
2025-06-08 19:16:05,195 - execution_queue - INFO - Started execution worker 1
2025-06-08 19:16:05,196 - execution_queue - INFO - Started execution worker 2
2025-06-08 19:16:05,196 - execution_queue - INFO - Started execution worker 3
2025-06-08 19:16:05,202 - simulation_logger - INFO - Simulation logger monitoring task started
2025-06-08 19:16:05,204 - execution_queue - INFO - Execution worker 0 started
2025-06-08 19:16:05,204 - execution_queue - INFO - Execution worker 1 started
2025-06-08 19:16:05,204 - execution_queue - INFO - Execution worker 2 started
2025-06-08 19:16:05,205 - execution_queue - INFO - Execution worker 3 started
2025-06-08 19:16:05,206 - simulation_logger - INFO - Starting simulation logger monitoring loop
2025-06-08 19:16:05,714 - websocket_manager - WARNING - Example URL is disabled. Skipping connection.
2025-06-08 19:16:05,714 - websocket_manager - WARNING - Tried all WebSocket URLs, none are supported.
2025-06-08 19:16:06,406 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:06,827 - __main__ - INFO - Fetched fresh SOL price: $149.99 (attempt 1)
2025-06-08 19:16:06,965 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:06,965 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:08,489 - __main__ - INFO - Found pool FwpQGgSN... with 27.645051 SOL
2025-06-08 19:16:08,490 - __main__ - INFO - Using calculated liquidity: $8,086 (x1.95)
2025-06-08 19:16:09,281 - __main__ - INFO - [FAST] Analysis completed in 2.87s
2025-06-08 19:16:09,283 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:09,284 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:09,284 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,086, MC: $13,087, Whale: 22.2%
2025-06-08 19:16:09,285 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001308, MC=$13,087, Liq=$8,086
2025-06-08 19:16:09,294 - execution_queue - INFO - Queued buy order for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with priority 5
2025-06-08 19:16:09,294 - execution_queue - INFO - Worker 0 executing buy for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump (Event ID: buy_GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump_1749390369.2907364)
2025-06-08 19:16:09,452 - trade_executor - INFO - Added buy_GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump_1749390369.2907364 to notified_buy_event_ids. Current size: 1
2025-06-08 19:16:09,453 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:16:09,453 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:16:09,453 - state_manager - INFO - Opened position for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: 0.4000 SOL + 0.0180 SOL fees @ 0.00001308, Tokens: 30581.0398
2025-06-08 19:16:09,454 - state_manager - INFO - Available SOL after position + fees: 1.0820
2025-06-08 19:16:09,454 - trade_executor - INFO - Position opened in state manager for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with 0.4000 SOL at $0.00001308
2025-06-08 19:16:09,454 - execution_queue - INFO - Worker 0 successfully executed buy for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump in 0.16s
2025-06-08 19:16:09,992 - main - WARNING - Failed to set console encoding: '_io.BufferedWriter' object has no attribute 'buffer'
2025-06-08 19:16:10,209 - simulation_logger - INFO - Registering new position for monitoring: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:10,210 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (0.9s ago)
2025-06-08 19:16:10,211 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (0.9s ago)
2025-06-08 19:16:15,225 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (5.9s ago)
2025-06-08 19:16:16,818 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:16,848 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:16,849 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:18,363 - __main__ - INFO - Found pool FwpQGgSN... with 27.369651 SOL
2025-06-08 19:16:18,364 - __main__ - INFO - Using calculated liquidity: $8,005 (x1.95)
2025-06-08 19:16:19,152 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:16:19,154 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:19,155 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:19,156 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,005, MC: $13,087, Whale: 22.2%
2025-06-08 19:16:19,157 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001308, MC=$13,087, Liq=$8,005
2025-06-08 19:16:20,240 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (1.1s ago)
2025-06-08 19:16:21,772 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:21,794 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:21,795 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:23,759 - __main__ - INFO - Found pool FwpQGgSN... with 26.587901 SOL
2025-06-08 19:16:23,759 - __main__ - INFO - Using calculated liquidity: $7,776 (x1.95)
2025-06-08 19:16:24,539 - __main__ - INFO - [FAST] Analysis completed in 2.77s
2025-06-08 19:16:24,541 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:24,542 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:24,542 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,776 < $8,000
2025-06-08 19:16:25,256 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (6.1s ago)
2025-06-08 19:16:27,147 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:27,170 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:27,171 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:28,648 - __main__ - INFO - Found pool FwpQGgSN... with 27.333611 SOL
2025-06-08 19:16:28,649 - __main__ - INFO - Using calculated liquidity: $7,995 (x1.95)
2025-06-08 19:16:29,467 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:16:29,469 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:29,470 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:29,471 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,995 < $8,000
2025-06-08 19:16:30,271 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (11.1s ago)
2025-06-08 19:16:32,069 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:32,092 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:32,092 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:33,529 - __main__ - INFO - Found pool FwpQGgSN... with 30.465119 SOL
2025-06-08 19:16:33,529 - __main__ - INFO - Using calculated liquidity: $8,910 (x1.95)
2025-06-08 19:16:34,349 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:16:34,351 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:34,352 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:34,353 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,910, MC: $13,087, Whale: 23.1%
2025-06-08 19:16:34,354 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001308, MC=$13,087, Liq=$8,910
2025-06-08 19:16:35,287 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (0.9s ago)
2025-06-08 19:16:36,960 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:36,987 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:36,987 - __main__ - INFO - DexScreener data available - Price: $1.308e-05, Volume 24h: $40,012
2025-06-08 19:16:38,507 - __main__ - INFO - Found pool FwpQGgSN... with 29.793861 SOL
2025-06-08 19:16:38,507 - __main__ - INFO - Using calculated liquidity: $8,714 (x1.95)
2025-06-08 19:16:40,182 - __main__ - INFO - [FAST] Analysis completed in 3.22s
2025-06-08 19:16:40,185 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:40,186 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:40,186 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,714, MC: $13,087, Whale: 23.1%
2025-06-08 19:16:40,187 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001308, MC=$13,087, Liq=$8,714
2025-06-08 19:16:40,303 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (0.1s ago)
2025-06-08 19:16:42,803 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:43,103 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:43,104 - __main__ - INFO - DexScreener data available - Price: $1.708e-05, Volume 24h: $45,334
2025-06-08 19:16:45,329 - __main__ - INFO - Found pool FwpQGgSN... with 30.365747 SOL
2025-06-08 19:16:45,330 - __main__ - INFO - Using calculated liquidity: $8,881 (x1.95)
2025-06-08 19:16:46,112 - __main__ - INFO - [FAST] Analysis completed in 3.31s
2025-06-08 19:16:46,114 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:46,115 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:46,116 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,881, MC: $17,085, Whale: 23.1%
2025-06-08 19:16:46,116 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001708, MC=$17,085, Liq=$8,881
2025-06-08 19:16:46,127 - execution_queue - INFO - Queued sell order for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with priority 2
2025-06-08 19:16:46,128 - execution_queue - WARNING - EMERGENCY SELL: Processing GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump immediately
2025-06-08 19:16:46,128 - execution_queue - INFO - Worker 0 executing sell for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump (Fraction: 100.0%, EventID: sell_GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump_TP1_hit_(30.6pct_>=_27.6pct)_1749390406)
2025-06-08 19:16:46,129 - trade_executor - INFO - SELL DEBUG: execute_sell called for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with token_amount=30581.039755351685, entry_price=1.308e-05, sell_fraction=1.0, EventID: sell_GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump_TP1_hit_(30.6pct_>=_27.6pct)_1749390406
2025-06-08 19:16:46,129 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=30581.039755351685 (token_amount=30581.039755351685, sell_fraction=1.0)
2025-06-08 19:16:46,133 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (0.0s ago)
2025-06-08 19:16:46,274 - trade_executor - INFO - SELL PRICE LOGGING: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump - Entry: 0.00001308, Current: 0.00001708, Est. PnL: 30.58%
2025-06-08 19:16:46,341 - trade_executor - INFO - SELL DEBUG: Added sell_GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump_TP1_hit_(30.6pct_>=_27.6pct)_1749390406 to notified_sell_event_ids. Current size: 1
2025-06-08 19:16:46,341 - execution_queue - INFO - Worker 0 successfully executed sell for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump in 0.21s
2025-06-08 19:16:46,343 - state_manager - INFO - SELL DEBUG: Attempting to close position for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with exit_price=1.708e-05, reason=TP1 hit (30.6% >= 27.6%), fraction=1.0
2025-06-08 19:16:46,343 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump']
2025-06-08 19:16:46,344 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0052 SOL, Platform: 0.0052 SOL
2025-06-08 19:16:46,345 - state_manager - INFO - [MONEY] SOL received: 0.5223 gross - 0.0104 fees = 0.5119 net
2025-06-08 19:16:46,346 - state_manager - INFO - SELL for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Sold 30581.0398 tokens @ 0.000017. PnL: 0.0889 SOL
2025-06-08 19:16:46,346 - state_manager - INFO - Position GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump closed. Total Profit SOL: 0.0889, Wins: 1, Losses: 0
2025-06-08 19:16:46,347 - state_manager - INFO - Recalculated available SOL: 1.5889
2025-06-08 19:16:47,444 - __main__ - INFO - [FAST] Analyzing token: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:47,861 - __main__ - INFO - Fetched data - SOL price: $149.99, Supply: 1,000,000,000
2025-06-08 19:16:47,862 - __main__ - INFO - DexScreener data available - Price: $1.708e-05, Volume 24h: $45,334
2025-06-08 19:16:49,273 - __main__ - INFO - Found pool FwpQGgSN... with 30.066470 SOL
2025-06-08 19:16:49,273 - __main__ - INFO - Using calculated liquidity: $8,794 (x1.95)
2025-06-08 19:16:50,036 - __main__ - INFO - [FAST] Analysis completed in 2.59s
2025-06-08 19:16:50,039 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:16:50,040 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:50,041 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,794, MC: $17,085, Whale: 23.1%
2025-06-08 19:16:50,041 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001708, MC=$17,085, Liq=$8,794
2025-06-08 19:16:51,147 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (1.1s ago)
2025-06-08 19:16:51,148 - simulation_logger - INFO - Logging closed position: GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump
2025-06-08 19:16:51,148 - __main__ - INFO - SIGNAL CACHE HIT: Skipping GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump: Recently analyzed (1.1s ago)
2025-06-08 19:16:51,150 - simulation_logger - INFO - Logged closed position for GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump with PNL: 30.58%
2025-06-08 19:17:17,016 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:17,305 - __main__ - INFO - Fetched fresh SOL price: $150.01 (attempt 1)
2025-06-08 19:17:17,795 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:17,796 - __main__ - INFO - DexScreener data available - Price: $1.224e-05, Volume 24h: $6,106
2025-06-08 19:17:19,331 - __main__ - INFO - Found pool FXEYJ1pG... with 28.825376 SOL
2025-06-08 19:17:19,331 - __main__ - INFO - Using calculated liquidity: $8,432 (x1.95)
2025-06-08 19:17:20,105 - __main__ - INFO - [FAST] Analysis completed in 3.09s
2025-06-08 19:17:20,107 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:20,108 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:20,109 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,432, MC: $12,249, Whale: 27.0%
2025-06-08 19:17:20,110 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001224, MC=$12,249, Liq=$8,432
2025-06-08 19:17:20,118 - execution_queue - INFO - Queued buy order for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with priority 5
2025-06-08 19:17:20,119 - execution_queue - INFO - Worker 1 executing buy for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump (Event ID: buy_BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump_1749390440.114496)
2025-06-08 19:17:20,196 - trade_executor - INFO - Added buy_BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump_1749390440.114496 to notified_buy_event_ids. Current size: 2
2025-06-08 19:17:20,198 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:17:20,199 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:17:20,199 - state_manager - INFO - Opened position for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: 0.4000 SOL + 0.0180 SOL fees @ 0.00001224, Tokens: 32679.7386
2025-06-08 19:17:20,200 - state_manager - INFO - Available SOL after position + fees: 1.1709
2025-06-08 19:17:20,201 - trade_executor - INFO - Position opened in state manager for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with 0.4000 SOL at $0.00001224
2025-06-08 19:17:20,201 - execution_queue - INFO - Worker 1 successfully executed buy for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump in 0.08s
2025-06-08 19:17:21,184 - simulation_logger - INFO - Registering new position for monitoring: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:21,184 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (1.1s ago)
2025-06-08 19:17:21,185 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (1.1s ago)
2025-06-08 19:17:23,481 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:23,504 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:23,505 - __main__ - INFO - DexScreener data available - Price: $1.224e-05, Volume 24h: $6,106
2025-06-08 19:17:25,032 - __main__ - INFO - Found pool FXEYJ1pG... with 30.964547 SOL
2025-06-08 19:17:25,033 - __main__ - INFO - Using calculated liquidity: $9,058 (x1.95)
2025-06-08 19:17:25,773 - __main__ - INFO - [FAST] Analysis completed in 2.29s
2025-06-08 19:17:25,775 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:25,776 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:25,777 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $9,058, MC: $12,249, Whale: 26.9%
2025-06-08 19:17:25,778 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001224, MC=$12,249, Liq=$9,058
2025-06-08 19:17:26,200 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (0.4s ago)
2025-06-08 19:17:28,387 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:28,409 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:28,410 - __main__ - INFO - DexScreener data available - Price: $1.224e-05, Volume 24h: $6,106
2025-06-08 19:17:29,951 - __main__ - INFO - Found pool FXEYJ1pG... with 32.789015 SOL
2025-06-08 19:17:29,952 - __main__ - INFO - Using calculated liquidity: $9,591 (x1.95)
2025-06-08 19:17:30,745 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:17:30,748 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:30,749 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:30,749 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $9,591, MC: $12,249, Whale: 26.0%
2025-06-08 19:17:30,750 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001224, MC=$12,249, Liq=$9,591
2025-06-08 19:17:30,817 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:17:31,609 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 999,999,999
2025-06-08 19:17:31,610 - __main__ - INFO - DexScreener data available - Price: $6.443e-05, Volume 24h: $148,271
2025-06-08 19:17:31,611 - __main__ - INFO - Using DexScreener liquidity: $26,400 (MC: $64,435)
2025-06-08 19:17:32,440 - __main__ - INFO - [FAST] Analysis completed in 1.62s
2025-06-08 19:17:32,464 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:32,465 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:17:32,466 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,400, MC: $64,435, Whale: 22.0%
2025-06-08 19:17:32,466 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006443, MC=$64,435, Liq=$26,400
2025-06-08 19:17:32,472 - execution_queue - INFO - Queued buy order for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with priority 5
2025-06-08 19:17:32,474 - execution_queue - INFO - Worker 1 executing buy for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump (Event ID: buy_E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump_1749390452.4702845)
2025-06-08 19:17:32,579 - trade_executor - INFO - Added buy_E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump_1749390452.4702845 to notified_buy_event_ids. Current size: 3
2025-06-08 19:17:32,580 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:17:32,581 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:17:32,582 - state_manager - INFO - Opened position for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: 0.4000 SOL + 0.0180 SOL fees @ 0.00006443, Tokens: 6208.2881
2025-06-08 19:17:32,582 - state_manager - INFO - Available SOL after position + fees: 0.7529
2025-06-08 19:17:32,583 - trade_executor - INFO - Position opened in state manager for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with 0.4000 SOL at $0.00006443
2025-06-08 19:17:32,583 - execution_queue - INFO - Worker 1 successfully executed buy for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump in 0.11s
2025-06-08 19:17:32,584 - simulation_logger - INFO - Registering new position for monitoring: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:17:32,584 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (0.1s ago)
2025-06-08 19:17:32,585 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (1.8s ago)
2025-06-08 19:17:32,585 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (0.1s ago)
2025-06-08 19:17:33,653 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:33,679 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:33,679 - __main__ - INFO - DexScreener data available - Price: $1.224e-05, Volume 24h: $6,106
2025-06-08 19:17:35,233 - __main__ - INFO - Found pool FXEYJ1pG... with 31.836215 SOL
2025-06-08 19:17:35,234 - __main__ - INFO - Using calculated liquidity: $9,313 (x1.95)
2025-06-08 19:17:36,011 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:17:36,015 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:36,016 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:36,016 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $9,313, MC: $12,249, Whale: 26.0%
2025-06-08 19:17:36,017 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001224, MC=$12,249, Liq=$9,313
2025-06-08 19:17:36,052 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:17:36,816 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:36,816 - __main__ - INFO - DexScreener data available - Price: $1.514e-05, Volume 24h: $7,747
2025-06-08 19:17:38,404 - __main__ - INFO - Found pool Bs1EcrUj... with 27.882185 SOL
2025-06-08 19:17:38,405 - __main__ - INFO - Using calculated liquidity: $8,156 (x1.95)
2025-06-08 19:17:39,115 - __main__ - INFO - [FAST] Analysis completed in 3.06s
2025-06-08 19:17:39,118 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:39,119 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:17:39,120 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,156, MC: $15,141, Whale: 17.8%
2025-06-08 19:17:39,120 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001514, MC=$15,141, Liq=$8,156
2025-06-08 19:17:39,128 - execution_queue - INFO - Queued buy order for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump with priority 5
2025-06-08 19:17:39,131 - execution_queue - INFO - Worker 1 executing buy for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump (Event ID: buy_8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump_1749390459.1248198)
2025-06-08 19:17:39,201 - trade_executor - INFO - Added buy_8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump_1749390459.1248198 to notified_buy_event_ids. Current size: 4
2025-06-08 19:17:39,203 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:17:39,204 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:17:39,204 - state_manager - INFO - Opened position for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: 0.4000 SOL + 0.0180 SOL fees @ 0.00001514, Tokens: 26420.0793
2025-06-08 19:17:39,205 - state_manager - INFO - Available SOL after position + fees: 0.3349
2025-06-08 19:17:39,206 - trade_executor - INFO - Position opened in state manager for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump with 0.4000 SOL at $0.00001514
2025-06-08 19:17:39,206 - execution_queue - INFO - Worker 1 successfully executed buy for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump in 0.07s
2025-06-08 19:17:39,208 - simulation_logger - INFO - Registering new position for monitoring: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:17:39,209 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.1s ago)
2025-06-08 19:17:39,209 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (3.2s ago)
2025-06-08 19:17:39,210 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.1s ago)
2025-06-08 19:17:39,211 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (6.7s ago)
2025-06-08 19:17:41,732 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:41,756 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:41,756 - __main__ - INFO - DexScreener data available - Price: $1.224e-05, Volume 24h: $6,106
2025-06-08 19:17:43,274 - __main__ - INFO - Found pool FXEYJ1pG... with 25.820964 SOL
2025-06-08 19:17:43,274 - __main__ - INFO - Using calculated liquidity: $7,553 (x1.95)
2025-06-08 19:17:44,032 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:17:44,035 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:44,036 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:44,036 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,553 < $8,000
2025-06-08 19:17:44,217 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (8.2s ago)
2025-06-08 19:17:44,218 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (5.1s ago)
2025-06-08 19:17:45,123 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:17:45,151 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 999,999,999
2025-06-08 19:17:45,152 - __main__ - INFO - DexScreener data available - Price: $6.443e-05, Volume 24h: $148,271
2025-06-08 19:17:45,153 - __main__ - INFO - Using DexScreener liquidity: $26,400 (MC: $64,435)
2025-06-08 19:17:46,135 - __main__ - INFO - [FAST] Analysis completed in 1.01s
2025-06-08 19:17:46,137 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:46,138 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:17:46,139 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,400, MC: $64,435, Whale: 22.0%
2025-06-08 19:17:46,139 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006443, MC=$64,435, Liq=$26,400
2025-06-08 19:17:47,232 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:17:47,258 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:47,259 - __main__ - INFO - DexScreener data available - Price: $1.514e-05, Volume 24h: $7,747
2025-06-08 19:17:48,776 - __main__ - INFO - Found pool Bs1EcrUj... with 28.435784 SOL
2025-06-08 19:17:48,776 - __main__ - INFO - Using calculated liquidity: $8,318 (x1.95)
2025-06-08 19:17:49,508 - __main__ - INFO - [FAST] Analysis completed in 2.27s
2025-06-08 19:17:49,510 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:49,511 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:17:49,512 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,318, MC: $15,141, Whale: 17.8%
2025-06-08 19:17:49,512 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001514, MC=$15,141, Liq=$8,318
2025-06-08 19:17:49,521 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (13.5s ago)
2025-06-08 19:17:49,522 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.0s ago)
2025-06-08 19:17:49,522 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.4s ago)
2025-06-08 19:17:52,123 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:52,577 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:52,578 - __main__ - INFO - DexScreener data available - Price: $1.658e-05, Volume 24h: $19,546
2025-06-08 19:17:55,277 - __main__ - INFO - Found pool FXEYJ1pG... with 28.323361 SOL
2025-06-08 19:17:55,278 - __main__ - INFO - Using calculated liquidity: $8,285 (x1.95)
2025-06-08 19:17:56,058 - __main__ - INFO - [FAST] Analysis completed in 3.93s
2025-06-08 19:17:56,060 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:56,061 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:56,062 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,285, MC: $16,582, Whale: 21.2%
2025-06-08 19:17:56,062 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001658, MC=$16,582, Liq=$8,285
2025-06-08 19:17:56,073 - execution_queue - INFO - Queued sell order for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with priority 2
2025-06-08 19:17:56,074 - execution_queue - WARNING - EMERGENCY SELL: Processing BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump immediately
2025-06-08 19:17:56,075 - execution_queue - INFO - Worker 1 executing sell for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump (Fraction: 100.0%, EventID: sell_BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump_TP1_hit_(35.5pct_>=_27.6pct)_1749390476)
2025-06-08 19:17:56,075 - trade_executor - INFO - SELL DEBUG: execute_sell called for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with token_amount=32679.738562091505, entry_price=1.224e-05, sell_fraction=1.0, EventID: sell_BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump_TP1_hit_(35.5pct_>=_27.6pct)_1749390476
2025-06-08 19:17:56,076 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=32679.738562091505 (token_amount=32679.738562091505, sell_fraction=1.0)
2025-06-08 19:17:56,079 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (0.0s ago)
2025-06-08 19:17:56,080 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (6.6s ago)
2025-06-08 19:17:56,080 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (9.9s ago)
2025-06-08 19:17:56,167 - trade_executor - INFO - SELL PRICE LOGGING: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump - Entry: 0.00001224, Current: 0.00001658, Est. PnL: 35.46%
2025-06-08 19:17:56,248 - trade_executor - INFO - SELL DEBUG: Added sell_BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump_TP1_hit_(35.5pct_>=_27.6pct)_1749390476 to notified_sell_event_ids. Current size: 2
2025-06-08 19:17:56,249 - execution_queue - INFO - Worker 1 successfully executed sell for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump in 0.17s
2025-06-08 19:17:56,250 - state_manager - INFO - SELL DEBUG: Attempting to close position for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with exit_price=1.658e-05, reason=TP1 hit (35.5% >= 27.6%), fraction=1.0
2025-06-08 19:17:56,250 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump', 'BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump', 'E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump', '8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump']
2025-06-08 19:17:56,252 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0054 SOL, Platform: 0.0054 SOL
2025-06-08 19:17:56,252 - state_manager - INFO - [MONEY] SOL received: 0.5418 gross - 0.0108 fees = 0.5310 net
2025-06-08 19:17:56,253 - state_manager - INFO - SELL for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Sold 32679.7386 tokens @ 0.000017. PnL: 0.1080 SOL
2025-06-08 19:17:56,254 - state_manager - INFO - Position BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump closed. Total Profit SOL: 0.1969, Wins: 2, Losses: 0
2025-06-08 19:17:56,254 - state_manager - INFO - Recalculated available SOL: 0.8969
2025-06-08 19:17:57,342 - __main__ - INFO - [FAST] Analyzing token: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:57,366 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:17:57,366 - __main__ - INFO - DexScreener data available - Price: $1.658e-05, Volume 24h: $19,546
2025-06-08 19:17:58,904 - __main__ - INFO - Found pool FXEYJ1pG... with 30.249947 SOL
2025-06-08 19:17:58,904 - __main__ - INFO - Using calculated liquidity: $8,849 (x1.95)
2025-06-08 19:17:59,692 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:17:59,694 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:17:59,695 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:17:59,696 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,849, MC: $16,582, Whale: 21.2%
2025-06-08 19:17:59,696 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001658, MC=$16,582, Liq=$8,849
2025-06-08 19:18:01,014 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:01,044 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 999,999,999
2025-06-08 19:18:01,044 - __main__ - INFO - DexScreener data available - Price: $6.443e-05, Volume 24h: $148,271
2025-06-08 19:18:01,045 - __main__ - INFO - Using DexScreener liquidity: $26,400 (MC: $64,435)
2025-06-08 19:18:01,952 - __main__ - INFO - [FAST] Analysis completed in 0.94s
2025-06-08 19:18:01,954 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:01,955 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:01,956 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,400, MC: $64,435, Whale: 22.0%
2025-06-08 19:18:01,956 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006443, MC=$64,435, Liq=$26,400
2025-06-08 19:18:01,964 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (2.3s ago)
2025-06-08 19:18:01,965 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (0.0s ago)
2025-06-08 19:18:01,965 - simulation_logger - INFO - Logging closed position: BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump
2025-06-08 19:18:01,966 - __main__ - INFO - SIGNAL CACHE HIT: Skipping BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump: Recently analyzed (2.3s ago)
2025-06-08 19:18:01,967 - simulation_logger - INFO - Logged closed position for BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump with PNL: 35.46%
2025-06-08 19:18:03,061 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:03,086 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:18:03,086 - __main__ - INFO - DexScreener data available - Price: $1.514e-05, Volume 24h: $7,747
2025-06-08 19:18:04,565 - __main__ - INFO - Found pool Bs1EcrUj... with 26.628543 SOL
2025-06-08 19:18:04,565 - __main__ - INFO - Using calculated liquidity: $7,789 (x1.95)
2025-06-08 19:18:05,344 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:18:05,346 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:05,347 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:05,347 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,789 < $8,000
2025-06-08 19:18:06,983 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (17.5s ago)
2025-06-08 19:18:07,951 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:08,048 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 999,999,999
2025-06-08 19:18:08,049 - __main__ - INFO - DexScreener data available - Price: $6.866e-05, Volume 24h: $149,361
2025-06-08 19:18:08,049 - __main__ - INFO - Using DexScreener liquidity: $27,262 (MC: $68,667)
2025-06-08 19:18:08,858 - __main__ - INFO - [FAST] Analysis completed in 0.91s
2025-06-08 19:18:08,860 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:08,861 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:08,862 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,262, MC: $68,667, Whale: 22.0%
2025-06-08 19:18:08,863 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006866, MC=$68,667, Liq=$27,262
2025-06-08 19:18:09,967 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:10,333 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 1,000,000,000
2025-06-08 19:18:10,334 - __main__ - INFO - DexScreener data available - Price: $1.513e-05, Volume 24h: $9,463
2025-06-08 19:18:12,310 - __main__ - INFO - Found pool Bs1EcrUj... with 27.568543 SOL
2025-06-08 19:18:12,310 - __main__ - INFO - Using calculated liquidity: $8,064 (x1.95)
2025-06-08 19:18:13,081 - __main__ - INFO - [FAST] Analysis completed in 3.11s
2025-06-08 19:18:13,083 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:13,084 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:13,085 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,064, MC: $15,139, Whale: 13.8%
2025-06-08 19:18:13,085 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001513, MC=$15,139, Liq=$8,064
2025-06-08 19:18:13,094 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.0s ago)
2025-06-08 19:18:13,095 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (4.2s ago)
2025-06-08 19:18:15,717 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:15,742 - __main__ - INFO - Fetched data - SOL price: $150.01, Supply: 999,999,999
2025-06-08 19:18:15,743 - __main__ - INFO - DexScreener data available - Price: $6.866e-05, Volume 24h: $149,361
2025-06-08 19:18:15,743 - __main__ - INFO - Using DexScreener liquidity: $27,262 (MC: $68,667)
2025-06-08 19:18:16,578 - __main__ - INFO - [FAST] Analysis completed in 0.86s
2025-06-08 19:18:16,580 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:16,581 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:16,582 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,262, MC: $68,667, Whale: 22.0%
2025-06-08 19:18:16,582 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006866, MC=$68,667, Liq=$27,262
2025-06-08 19:18:17,686 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:17,960 - __main__ - INFO - Fetched fresh SOL price: $150.05 (attempt 1)
2025-06-08 19:18:17,961 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:17,962 - __main__ - INFO - DexScreener data available - Price: $1.513e-05, Volume 24h: $9,463
2025-06-08 19:18:19,431 - __main__ - INFO - Found pool Bs1EcrUj... with 28.248543 SOL
2025-06-08 19:18:19,432 - __main__ - INFO - Using calculated liquidity: $8,265 (x1.95)
2025-06-08 19:18:20,185 - __main__ - INFO - [FAST] Analysis completed in 2.50s
2025-06-08 19:18:20,187 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:20,188 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:20,189 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,265, MC: $15,139, Whale: 13.8%
2025-06-08 19:18:20,190 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001513, MC=$15,139, Liq=$8,265
2025-06-08 19:18:20,198 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.0s ago)
2025-06-08 19:18:20,199 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.6s ago)
2025-06-08 19:18:22,795 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:22,827 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:18:22,827 - __main__ - INFO - DexScreener data available - Price: $6.866e-05, Volume 24h: $149,361
2025-06-08 19:18:22,828 - __main__ - INFO - Using DexScreener liquidity: $27,262 (MC: $68,667)
2025-06-08 19:18:23,650 - __main__ - INFO - [FAST] Analysis completed in 0.85s
2025-06-08 19:18:23,653 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:23,654 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:23,655 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,262, MC: $68,667, Whale: 22.0%
2025-06-08 19:18:23,655 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006866, MC=$68,667, Liq=$27,262
2025-06-08 19:18:24,749 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:24,771 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:24,771 - __main__ - INFO - DexScreener data available - Price: $1.513e-05, Volume 24h: $9,463
2025-06-08 19:18:26,269 - __main__ - INFO - Found pool Bs1EcrUj... with 29.648935 SOL
2025-06-08 19:18:26,270 - __main__ - INFO - Using calculated liquidity: $8,675 (x1.95)
2025-06-08 19:18:27,030 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:18:27,032 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:27,033 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:27,034 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,675, MC: $15,139, Whale: 13.8%
2025-06-08 19:18:27,034 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001513, MC=$15,139, Liq=$8,675
2025-06-08 19:18:27,043 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (0.0s ago)
2025-06-08 19:18:27,044 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.4s ago)
2025-06-08 19:18:29,639 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:29,662 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:18:29,662 - __main__ - INFO - DexScreener data available - Price: $6.866e-05, Volume 24h: $149,361
2025-06-08 19:18:29,663 - __main__ - INFO - Using DexScreener liquidity: $27,262 (MC: $68,667)
2025-06-08 19:18:30,512 - __main__ - INFO - [FAST] Analysis completed in 0.87s
2025-06-08 19:18:30,514 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:30,515 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:30,516 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,262, MC: $68,667, Whale: 22.0%
2025-06-08 19:18:30,517 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006866, MC=$68,667, Liq=$27,262
2025-06-08 19:18:31,623 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:31,649 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:31,650 - __main__ - INFO - DexScreener data available - Price: $1.513e-05, Volume 24h: $9,463
2025-06-08 19:18:33,134 - __main__ - INFO - Found pool Bs1EcrUj... with 22.990851 SOL
2025-06-08 19:18:33,134 - __main__ - INFO - Using calculated liquidity: $6,727 (x1.95)
2025-06-08 19:18:33,876 - __main__ - INFO - [FAST] Analysis completed in 2.25s
2025-06-08 19:18:34,601 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:34,602 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:34,603 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,727 < $8,000
2025-06-08 19:18:34,605 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (7.6s ago)
2025-06-08 19:18:34,606 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (4.1s ago)
2025-06-08 19:18:37,202 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:37,225 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:18:37,226 - __main__ - INFO - DexScreener data available - Price: $6.866e-05, Volume 24h: $149,361
2025-06-08 19:18:37,226 - __main__ - INFO - Using DexScreener liquidity: $27,262 (MC: $68,667)
2025-06-08 19:18:38,067 - __main__ - INFO - [FAST] Analysis completed in 0.86s
2025-06-08 19:18:38,070 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:38,071 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:38,071 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,262, MC: $68,667, Whale: 22.0%
2025-06-08 19:18:38,072 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006866, MC=$68,667, Liq=$27,262
2025-06-08 19:18:39,170 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:39,196 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:39,197 - __main__ - INFO - DexScreener data available - Price: $1.513e-05, Volume 24h: $9,463
2025-06-08 19:18:40,741 - __main__ - INFO - Found pool Bs1EcrUj... with 23.705627 SOL
2025-06-08 19:18:40,741 - __main__ - INFO - Using calculated liquidity: $6,936 (x1.95)
2025-06-08 19:18:41,542 - __main__ - INFO - [FAST] Analysis completed in 2.37s
2025-06-08 19:18:41,544 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:41,545 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:41,545 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,936 < $8,000
2025-06-08 19:18:41,548 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (14.5s ago)
2025-06-08 19:18:41,549 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.5s ago)
2025-06-08 19:18:42,241 - telethon.client.users - WARNING - Telegram is having internal issues PersistentTimestampOutdatedError: Persistent timestamp outdated (caused by GetChannelDifferenceRequest)
2025-06-08 19:18:44,155 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:44,237 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:18:44,238 - __main__ - INFO - DexScreener data available - Price: $7.075e-05, Volume 24h: $151,262
2025-06-08 19:18:44,238 - __main__ - INFO - Using DexScreener liquidity: $27,700 (MC: $70,760)
2025-06-08 19:18:45,080 - __main__ - INFO - [FAST] Analysis completed in 0.92s
2025-06-08 19:18:45,082 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:45,083 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:45,084 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,700, MC: $70,760, Whale: 22.0%
2025-06-08 19:18:45,084 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00007075, MC=$70,760, Liq=$27,700
2025-06-08 19:18:46,170 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:46,537 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:46,538 - __main__ - INFO - DexScreener data available - Price: $1.418e-05, Volume 24h: $11,681
2025-06-08 19:18:48,104 - __main__ - INFO - Found pool Bs1EcrUj... with 25.236586 SOL
2025-06-08 19:18:48,105 - __main__ - INFO - Using calculated liquidity: $7,384 (x1.95)
2025-06-08 19:18:48,871 - __main__ - INFO - [FAST] Analysis completed in 2.70s
2025-06-08 19:18:48,876 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:48,879 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:48,880 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,384 < $8,000
2025-06-08 19:18:48,884 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (21.8s ago)
2025-06-08 19:18:48,885 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.8s ago)
2025-06-08 19:18:51,577 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:51,615 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:18:51,616 - __main__ - INFO - DexScreener data available - Price: $7.075e-05, Volume 24h: $151,262
2025-06-08 19:18:51,617 - __main__ - INFO - Using DexScreener liquidity: $27,700 (MC: $70,760)
2025-06-08 19:18:53,115 - __main__ - INFO - [FAST] Analysis completed in 1.54s
2025-06-08 19:18:53,117 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:53,118 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:18:53,118 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,700, MC: $70,760, Whale: 22.0%
2025-06-08 19:18:53,119 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00007075, MC=$70,760, Liq=$27,700
2025-06-08 19:18:53,889 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (0.8s ago)
2025-06-08 19:18:54,217 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:54,242 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:54,242 - __main__ - INFO - DexScreener data available - Price: $1.418e-05, Volume 24h: $11,681
2025-06-08 19:18:55,741 - __main__ - INFO - Found pool Bs1EcrUj... with 26.746586 SOL
2025-06-08 19:18:55,742 - __main__ - INFO - Using calculated liquidity: $7,826 (x1.95)
2025-06-08 19:18:56,530 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:18:56,532 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:18:56,533 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:56,534 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $7,826 < $8,000
2025-06-08 19:18:58,904 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:18:58,926 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:18:58,927 - __main__ - INFO - DexScreener data available - Price: $1.418e-05, Volume 24h: $11,681
2025-06-08 19:19:00,444 - __main__ - INFO - Found pool Bs1EcrUj... with 27.706586 SOL
2025-06-08 19:19:00,445 - __main__ - INFO - Using calculated liquidity: $8,107 (x1.95)
2025-06-08 19:19:01,933 - __main__ - INFO - [FAST] Analysis completed in 3.03s
2025-06-08 19:19:01,935 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:01,936 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:01,937 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,107, MC: $14,189, Whale: 7.0%
2025-06-08 19:19:01,938 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001418, MC=$14,189, Liq=$8,107
2025-06-08 19:19:03,030 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:03,053 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:19:03,054 - __main__ - INFO - DexScreener data available - Price: $1.418e-05, Volume 24h: $11,681
2025-06-08 19:19:04,577 - __main__ - INFO - Found pool Bs1EcrUj... with 28.016586 SOL
2025-06-08 19:19:04,577 - __main__ - INFO - Using calculated liquidity: $8,198 (x1.95)
2025-06-08 19:19:05,306 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:19:05,308 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:05,309 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:05,310 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $8,198, MC: $14,189, Whale: 7.0%
2025-06-08 19:19:05,311 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001418, MC=$14,189, Liq=$8,198
2025-06-08 19:19:06,952 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (1.6s ago)
2025-06-08 19:19:07,920 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:08,463 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:19:08,464 - __main__ - INFO - DexScreener data available - Price: $7.075e-05, Volume 24h: $151,262
2025-06-08 19:19:08,464 - __main__ - INFO - Using DexScreener liquidity: $27,700 (MC: $70,760)
2025-06-08 19:19:09,298 - __main__ - INFO - [FAST] Analysis completed in 0.86s
2025-06-08 19:19:09,300 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:09,301 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:09,301 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $27,700, MC: $70,760, Whale: 22.0%
2025-06-08 19:19:09,302 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00007075, MC=$70,760, Liq=$27,700
2025-06-08 19:19:10,405 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:10,427 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:19:10,428 - __main__ - INFO - DexScreener data available - Price: $1.418e-05, Volume 24h: $11,681
2025-06-08 19:19:11,947 - __main__ - INFO - Found pool Bs1EcrUj... with 1.536766 SOL
2025-06-08 19:19:11,948 - __main__ - INFO - Using calculated liquidity: $450 (x1.95)
2025-06-08 19:19:12,727 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:19:12,729 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:12,730 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:12,731 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $450 < $8,000
2025-06-08 19:19:12,734 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (7.4s ago)
2025-06-08 19:19:12,734 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.4s ago)
2025-06-08 19:19:15,342 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:15,412 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 999,999,999
2025-06-08 19:19:15,413 - __main__ - INFO - DexScreener data available - Price: $6.379e-05, Volume 24h: $153,513
2025-06-08 19:19:15,413 - __main__ - INFO - Using DexScreener liquidity: $26,328 (MC: $63,795)
2025-06-08 19:19:16,221 - __main__ - INFO - [FAST] Analysis completed in 0.88s
2025-06-08 19:19:16,223 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:16,224 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:16,225 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,328, MC: $63,795, Whale: 22.0%
2025-06-08 19:19:16,226 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006379, MC=$63,795, Liq=$26,328
2025-06-08 19:19:17,327 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:17,646 - __main__ - INFO - Fetched data - SOL price: $150.05, Supply: 1,000,000,000
2025-06-08 19:19:17,647 - __main__ - INFO - DexScreener data available - Price: $4.643e-06, Volume 24h: $16,341
2025-06-08 19:19:19,048 - __main__ - INFO - Found pool Bs1EcrUj... with 1.536766 SOL
2025-06-08 19:19:19,049 - __main__ - INFO - Using calculated liquidity: $450 (x1.95)
2025-06-08 19:19:19,777 - __main__ - INFO - [FAST] Analysis completed in 2.45s
2025-06-08 19:19:19,781 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:19,783 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:19,783 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $450 < $8,000
2025-06-08 19:19:19,785 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (14.5s ago)
2025-06-08 19:19:19,786 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.6s ago)
2025-06-08 19:19:22,389 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:22,686 - __main__ - INFO - Fetched fresh SOL price: $150.07 (attempt 1)
2025-06-08 19:19:22,687 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:19:22,688 - __main__ - INFO - DexScreener data available - Price: $6.379e-05, Volume 24h: $153,513
2025-06-08 19:19:22,689 - __main__ - INFO - Using DexScreener liquidity: $26,328 (MC: $63,795)
2025-06-08 19:19:23,563 - __main__ - INFO - [FAST] Analysis completed in 1.17s
2025-06-08 19:19:23,565 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:23,566 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:23,567 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,328, MC: $63,795, Whale: 22.0%
2025-06-08 19:19:23,568 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006379, MC=$63,795, Liq=$26,328
2025-06-08 19:19:23,855 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:24,629 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:24,907 - __main__ - INFO - DexScreener data available - Price: $1.736e-05, Volume 24h: $7,627
2025-06-08 19:19:26,451 - __main__ - INFO - Found pool 9r8sFQQ9... with 39.630822 SOL
2025-06-08 19:19:26,452 - __main__ - INFO - Using calculated liquidity: $11,597 (x1.95)
2025-06-08 19:19:27,199 - __main__ - INFO - [FAST] Analysis completed in 3.34s
2025-06-08 19:19:27,202 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:27,203 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:27,204 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $11,597, MC: $17,363, Whale: 27.4%
2025-06-08 19:19:27,204 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001736, MC=$17,363, Liq=$11,597
2025-06-08 19:19:27,213 - execution_queue - INFO - Queued buy order for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump with priority 5
2025-06-08 19:19:27,217 - execution_queue - INFO - Worker 2 executing buy for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump (Event ID: buy_9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump_1749390567.2100365)
2025-06-08 19:19:27,695 - trade_executor - INFO - Added buy_9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump_1749390567.2100365 to notified_buy_event_ids. Current size: 5
2025-06-08 19:19:27,696 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:19:27,697 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:19:27,697 - state_manager - INFO - Opened position for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: 0.4000 SOL + 0.0180 SOL fees @ 0.00001736, Tokens: 23041.4747
2025-06-08 19:19:27,698 - state_manager - INFO - Available SOL after position + fees: 0.4789
2025-06-08 19:19:27,699 - trade_executor - INFO - Position opened in state manager for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump with 0.4000 SOL at $0.00001736
2025-06-08 19:19:27,699 - execution_queue - INFO - Worker 2 successfully executed buy for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump in 0.48s
2025-06-08 19:19:27,700 - simulation_logger - INFO - Registering new position for monitoring: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:27,700 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: Recently analyzed (0.5s ago)
2025-06-08 19:19:27,701 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: Recently analyzed (0.5s ago)
2025-06-08 19:19:27,702 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (4.1s ago)
2025-06-08 19:19:27,702 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (22.4s ago)
2025-06-08 19:19:29,811 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:29,837 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:19:29,838 - __main__ - INFO - DexScreener data available - Price: $6.379e-05, Volume 24h: $153,513
2025-06-08 19:19:29,838 - __main__ - INFO - Using DexScreener liquidity: $26,328 (MC: $63,795)
2025-06-08 19:19:30,760 - __main__ - INFO - [FAST] Analysis completed in 0.95s
2025-06-08 19:19:30,762 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:30,763 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:30,764 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,328, MC: $63,795, Whale: 22.0%
2025-06-08 19:19:30,764 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006379, MC=$63,795, Liq=$26,328
2025-06-08 19:19:31,858 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:31,883 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:31,884 - __main__ - INFO - DexScreener data available - Price: $4.643e-06, Volume 24h: $16,341
2025-06-08 19:19:33,441 - __main__ - INFO - Found pool Bs1EcrUj... with 0.162579 SOL
2025-06-08 19:19:33,442 - __main__ - INFO - Using calculated liquidity: $48 (x1.95)
2025-06-08 19:19:34,168 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:19:34,170 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:34,171 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:34,172 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $48 < $8,000
2025-06-08 19:19:34,173 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.4s ago)
2025-06-08 19:19:34,174 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Recently analyzed (28.9s ago)
2025-06-08 19:19:35,264 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:35,286 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:35,287 - __main__ - INFO - DexScreener data available - Price: $1.736e-05, Volume 24h: $7,627
2025-06-08 19:19:36,730 - __main__ - INFO - Found pool 9r8sFQQ9... with 41.296593 SOL
2025-06-08 19:19:36,731 - __main__ - INFO - Using calculated liquidity: $12,085 (x1.95)
2025-06-08 19:19:37,556 - __main__ - INFO - [FAST] Analysis completed in 2.29s
2025-06-08 19:19:37,558 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:37,559 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:37,559 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $12,085, MC: $17,363, Whale: 27.4%
2025-06-08 19:19:37,560 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001736, MC=$17,363, Liq=$12,085
2025-06-08 19:19:39,186 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: Recently analyzed (1.6s ago)
2025-06-08 19:19:39,187 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:39,209 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:39,209 - __main__ - INFO - DexScreener data available - Price: $4.643e-06, Volume 24h: $16,341
2025-06-08 19:19:40,727 - __main__ - INFO - Found pool Bs1EcrUj... with 0.162579 SOL
2025-06-08 19:19:40,727 - __main__ - INFO - Using calculated liquidity: $48 (x1.95)
2025-06-08 19:19:41,520 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:19:41,522 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:41,523 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:41,523 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $48 < $8,000
2025-06-08 19:19:41,524 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:42,686 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:42,708 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:42,709 - __main__ - INFO - DexScreener data available - Price: $4.643e-06, Volume 24h: $16,341
2025-06-08 19:19:44,251 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:19:44,252 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:19:44,983 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:19:44,985 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:44,986 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:44,987 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:19:46,077 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:46,102 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:46,103 - __main__ - INFO - DexScreener data available - Price: $1.736e-05, Volume 24h: $7,627
2025-06-08 19:19:47,605 - __main__ - INFO - Found pool 9r8sFQQ9... with 43.164211 SOL
2025-06-08 19:19:47,605 - __main__ - INFO - Using calculated liquidity: $12,631 (x1.95)
2025-06-08 19:19:48,361 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:19:48,363 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:48,364 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:19:48,364 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $12,631, MC: $17,363, Whale: 27.5%
2025-06-08 19:19:48,365 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00001736, MC=$17,363, Liq=$12,631
2025-06-08 19:19:48,373 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: Recently analyzed (0.0s ago)
2025-06-08 19:19:48,374 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (17.6s ago)
2025-06-08 19:19:48,374 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:48,396 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:48,396 - __main__ - INFO - DexScreener data available - Price: $4.643e-06, Volume 24h: $16,341
2025-06-08 19:19:49,910 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:19:49,911 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:19:50,703 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:19:50,705 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:50,706 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:50,707 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:19:50,707 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:52,436 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:52,525 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:19:52,526 - __main__ - INFO - DexScreener data available - Price: $6.153e-05, Volume 24h: $154,705
2025-06-08 19:19:52,527 - __main__ - INFO - Using DexScreener liquidity: $25,849 (MC: $61,539)
2025-06-08 19:19:53,372 - __main__ - INFO - [FAST] Analysis completed in 0.94s
2025-06-08 19:19:53,375 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:53,376 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:19:53,376 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,849, MC: $61,539, Whale: 22.2%
2025-06-08 19:19:53,377 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006153, MC=$61,539, Liq=$25,849
2025-06-08 19:19:54,483 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:54,823 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:54,823 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:19:56,857 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:19:56,858 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:19:57,628 - __main__ - INFO - [FAST] Analysis completed in 3.14s
2025-06-08 19:19:57,630 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:57,631 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:57,632 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:19:57,633 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (4.3s ago)
2025-06-08 19:19:57,634 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:57,657 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:19:57,657 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:19:59,202 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:19:59,202 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:19:59,990 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:19:59,992 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:19:59,993 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:19:59,994 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:19:59,994 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:01,342 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:01,370 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:20:01,371 - __main__ - INFO - DexScreener data available - Price: $6.153e-05, Volume 24h: $154,705
2025-06-08 19:20:01,372 - __main__ - INFO - Using DexScreener liquidity: $25,849 (MC: $61,539)
2025-06-08 19:20:02,206 - __main__ - INFO - [FAST] Analysis completed in 0.86s
2025-06-08 19:20:02,208 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:02,209 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:02,210 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,849, MC: $61,539, Whale: 22.2%
2025-06-08 19:20:02,210 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006153, MC=$61,539, Liq=$25,849
2025-06-08 19:20:03,311 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:03,334 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:03,334 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:04,897 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:20:04,898 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:20:05,653 - __main__ - INFO - [FAST] Analysis completed in 2.34s
2025-06-08 19:20:05,655 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:05,656 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:05,656 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:20:05,658 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.4s ago)
2025-06-08 19:20:05,658 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:05,680 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:05,681 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:07,144 - __main__ - INFO - Found pool Bs1EcrUj... with 0.137164 SOL
2025-06-08 19:20:07,145 - __main__ - INFO - Using calculated liquidity: $40 (x1.95)
2025-06-08 19:20:07,887 - __main__ - INFO - [FAST] Analysis completed in 2.23s
2025-06-08 19:20:07,889 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:07,890 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:07,891 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $40 < $8,000
2025-06-08 19:20:07,891 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:09,373 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:09,397 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:20:09,398 - __main__ - INFO - DexScreener data available - Price: $6.153e-05, Volume 24h: $154,705
2025-06-08 19:20:09,399 - __main__ - INFO - Using DexScreener liquidity: $25,849 (MC: $61,539)
2025-06-08 19:20:10,181 - __main__ - INFO - [FAST] Analysis completed in 0.81s
2025-06-08 19:20:10,183 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:10,184 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:10,185 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,849, MC: $61,539, Whale: 22.5%
2025-06-08 19:20:10,185 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006153, MC=$61,539, Liq=$25,849
2025-06-08 19:20:11,280 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:11,306 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:11,307 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:12,836 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:12,837 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:13,665 - __main__ - INFO - [FAST] Analysis completed in 2.38s
2025-06-08 19:20:13,667 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:13,668 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:13,669 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:13,670 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.5s ago)
2025-06-08 19:20:13,671 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:13,702 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:13,703 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:15,200 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:15,201 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:15,975 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:20:15,977 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:15,978 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:15,979 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:15,980 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:17,373 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:17,403 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 999,999,999
2025-06-08 19:20:17,403 - __main__ - INFO - DexScreener data available - Price: $6.153e-05, Volume 24h: $154,705
2025-06-08 19:20:17,404 - __main__ - INFO - Using DexScreener liquidity: $25,849 (MC: $61,539)
2025-06-08 19:20:18,295 - __main__ - INFO - [FAST] Analysis completed in 0.92s
2025-06-08 19:20:18,297 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:18,298 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:18,299 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,849, MC: $61,539, Whale: 22.5%
2025-06-08 19:20:18,299 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00006153, MC=$61,539, Liq=$25,849
2025-06-08 19:20:19,405 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:19,437 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:19,438 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:20,945 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:20,946 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:21,748 - __main__ - INFO - [FAST] Analysis completed in 2.34s
2025-06-08 19:20:21,750 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:21,751 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:21,752 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:21,753 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.5s ago)
2025-06-08 19:20:21,754 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:21,775 - __main__ - INFO - Fetched data - SOL price: $150.07, Supply: 1,000,000,000
2025-06-08 19:20:21,775 - __main__ - INFO - DexScreener data available - Price: $4.251e-06, Volume 24h: $16,543
2025-06-08 19:20:23,293 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:23,294 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:24,066 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:20:24,068 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:24,069 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:24,070 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:24,071 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:25,467 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:25,764 - __main__ - INFO - Fetched fresh SOL price: $150.1 (attempt 1)
2025-06-08 19:20:25,766 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 999,999,999
2025-06-08 19:20:25,766 - __main__ - INFO - DexScreener data available - Price: $5.909e-05, Volume 24h: $156,633
2025-06-08 19:20:25,767 - __main__ - INFO - Using DexScreener liquidity: $25,326 (MC: $59,093)
2025-06-08 19:20:26,575 - __main__ - INFO - [FAST] Analysis completed in 1.11s
2025-06-08 19:20:26,577 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:26,578 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:26,578 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,326, MC: $59,093, Whale: 21.4%
2025-06-08 19:20:26,579 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00005909, MC=$59,093, Liq=$25,326
2025-06-08 19:20:27,686 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:28,000 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:28,001 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:29,575 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:29,576 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:30,387 - __main__ - INFO - [FAST] Analysis completed in 2.70s
2025-06-08 19:20:30,389 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:30,390 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:30,391 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:30,392 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (3.8s ago)
2025-06-08 19:20:30,393 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:30,417 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:30,418 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:31,959 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:31,960 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:32,713 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:20:32,715 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:32,716 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:32,716 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:32,717 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:34,108 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:34,136 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 999,999,999
2025-06-08 19:20:34,137 - __main__ - INFO - DexScreener data available - Price: $5.909e-05, Volume 24h: $156,633
2025-06-08 19:20:34,137 - __main__ - INFO - Using DexScreener liquidity: $25,326 (MC: $59,093)
2025-06-08 19:20:35,006 - __main__ - INFO - [FAST] Analysis completed in 0.90s
2025-06-08 19:20:35,010 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:35,011 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:35,012 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,326, MC: $59,093, Whale: 21.4%
2025-06-08 19:20:35,012 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00005909, MC=$59,093, Liq=$25,326
2025-06-08 19:20:36,033 - execution_queue - INFO - Queued sell order for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with priority 0
2025-06-08 19:20:36,108 - execution_queue - WARNING - EMERGENCY SELL: Processing E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump immediately
2025-06-08 19:20:36,108 - execution_queue - INFO - Worker 2 executing sell for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump (Fraction: 100.0%, EventID: sell_E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump_MANDATORY_TIMEOUT_EXIT_(3.0_min_>=_3_min)_-_FREEING_SLOT_1749390635)
2025-06-08 19:20:36,109 - trade_executor - INFO - SELL DEBUG: execute_sell called for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with token_amount=6208.288064566195, entry_price=6.443e-05, sell_fraction=1.0, EventID: sell_E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump_MANDATORY_TIMEOUT_EXIT_(3.0_min_>=_3_min)_-_FREEING_SLOT_1749390635
2025-06-08 19:20:36,110 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=6208.288064566195 (token_amount=6208.288064566195, sell_fraction=1.0)
2025-06-08 19:20:36,192 - trade_executor - INFO - SELL PRICE LOGGING: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump - Entry: 0.00006443, Current: 0.00005909, Est. PnL: -8.29%
2025-06-08 19:20:36,237 - trade_executor - INFO - SELL DEBUG: Added sell_E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump_MANDATORY_TIMEOUT_EXIT_(3.0_min_>=_3_min)_-_FREEING_SLOT_1749390635 to notified_sell_event_ids. Current size: 3
2025-06-08 19:20:36,238 - execution_queue - INFO - Worker 2 successfully executed sell for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump in 0.13s
2025-06-08 19:20:36,239 - state_manager - INFO - SELL DEBUG: Attempting to close position for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with exit_price=5.909e-05, reason=MANDATORY TIMEOUT EXIT (3.0 min >= 3 min) - FREEING SLOT, fraction=1.0
2025-06-08 19:20:36,240 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump', 'BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump', 'E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump', '8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump', '9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump']
2025-06-08 19:20:36,241 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0037 SOL, Platform: 0.0037 SOL
2025-06-08 19:20:36,242 - state_manager - INFO - [MONEY] SOL received: 0.3668 gross - 0.0073 fees = 0.3595 net
2025-06-08 19:20:36,242 - state_manager - INFO - SELL for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Sold 6208.2881 tokens @ 0.000059. PnL: -0.0635 SOL
2025-06-08 19:20:36,243 - state_manager - INFO - Position E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump closed. Total Profit SOL: 0.1334, Wins: 2, Losses: 1
2025-06-08 19:20:36,244 - state_manager - INFO - Recalculated available SOL: 0.8334
2025-06-08 19:20:37,342 - __main__ - INFO - [FAST] Analyzing token: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:37,376 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 999,999,999
2025-06-08 19:20:37,378 - __main__ - INFO - DexScreener data available - Price: $5.909e-05, Volume 24h: $156,633
2025-06-08 19:20:37,378 - __main__ - INFO - Using DexScreener liquidity: $25,326 (MC: $59,093)
2025-06-08 19:20:38,178 - __main__ - INFO - [FAST] Analysis completed in 0.84s
2025-06-08 19:20:38,181 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:38,182 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:38,183 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $25,326, MC: $59,093, Whale: 21.4%
2025-06-08 19:20:38,184 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00005909, MC=$59,093, Liq=$25,326
2025-06-08 19:20:38,193 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:38,587 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:38,588 - __main__ - INFO - DexScreener data available - Price: $2.711e-05, Volume 24h: $10,774
2025-06-08 19:20:40,085 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.341350 SOL
2025-06-08 19:20:40,086 - __main__ - INFO - Using calculated liquidity: $100 (x1.95)
2025-06-08 19:20:40,858 - __main__ - INFO - [FAST] Analysis completed in 2.66s
2025-06-08 19:20:40,861 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:40,862 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:40,863 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $100 < $8,000
2025-06-08 19:20:40,863 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:40,864 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (2.7s ago)
2025-06-08 19:20:40,864 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:40,887 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:40,887 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:42,363 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:42,364 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:43,145 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:20:43,147 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:43,148 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:43,148 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:43,149 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:43,149 - simulation_logger - INFO - Logging closed position: E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump
2025-06-08 19:20:43,150 - __main__ - INFO - SIGNAL CACHE HIT: Skipping E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump: Recently analyzed (5.0s ago)
2025-06-08 19:20:43,151 - simulation_logger - INFO - Logged closed position for E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump with PNL: -8.29%
2025-06-08 19:20:44,514 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:44,536 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:44,537 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:46,023 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:46,024 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:46,804 - __main__ - INFO - [FAST] Analysis completed in 2.29s
2025-06-08 19:20:46,809 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:46,810 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:46,811 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:47,905 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:47,932 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:47,933 - __main__ - INFO - DexScreener data available - Price: $2.711e-05, Volume 24h: $10,774
2025-06-08 19:20:49,450 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.225283 SOL
2025-06-08 19:20:49,451 - __main__ - INFO - Using calculated liquidity: $66 (x1.95)
2025-06-08 19:20:50,217 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:20:50,219 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:50,220 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:50,221 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $66 < $8,000
2025-06-08 19:20:50,224 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:50,258 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:50,258 - __main__ - INFO - DexScreener data available - Price: $2.711e-05, Volume 24h: $10,774
2025-06-08 19:20:51,754 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.225283 SOL
2025-06-08 19:20:51,754 - __main__ - INFO - Using calculated liquidity: $66 (x1.95)
2025-06-08 19:20:52,508 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:20:52,511 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:52,513 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:52,513 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $66 < $8,000
2025-06-08 19:20:52,514 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:20:52,515 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:52,538 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:52,538 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:54,733 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:54,734 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:55,511 - __main__ - INFO - [FAST] Analysis completed in 3.00s
2025-06-08 19:20:55,515 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:55,516 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:55,517 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:20:55,518 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:57,390 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:57,422 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:20:57,423 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:20:58,941 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:20:58,942 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:20:59,767 - __main__ - INFO - [FAST] Analysis completed in 2.38s
2025-06-08 19:20:59,769 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:20:59,770 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:20:59,770 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:21:00,530 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:00,897 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:00,897 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:02,441 - __main__ - INFO - Found pool Bs1EcrUj... with 0.123957 SOL
2025-06-08 19:21:02,442 - __main__ - INFO - Using calculated liquidity: $36 (x1.95)
2025-06-08 19:21:03,202 - __main__ - INFO - [FAST] Analysis completed in 2.67s
2025-06-08 19:21:03,204 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:03,205 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:03,206 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $36 < $8,000
2025-06-08 19:21:03,207 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:04,405 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:04,432 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:04,433 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:06,022 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:06,023 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:06,795 - __main__ - INFO - [FAST] Analysis completed in 2.39s
2025-06-08 19:21:06,797 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:06,798 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:06,799 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:07,890 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:07,994 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:07,995 - __main__ - INFO - DexScreener data available - Price: $2.711e-05, Volume 24h: $10,774
2025-06-08 19:21:09,510 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.305582 SOL
2025-06-08 19:21:09,511 - __main__ - INFO - Using calculated liquidity: $89 (x1.95)
2025-06-08 19:21:10,210 - __main__ - INFO - [FAST] Analysis completed in 2.26s
2025-06-08 19:21:10,212 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:10,213 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:10,214 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $89 < $8,000
2025-06-08 19:21:10,217 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:10,612 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:10,618 - __main__ - INFO - DexScreener data available - Price: $4.299e-06, Volume 24h: $19,081
2025-06-08 19:21:12,186 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.305582 SOL
2025-06-08 19:21:12,187 - __main__ - INFO - Using calculated liquidity: $89 (x1.95)
2025-06-08 19:21:12,937 - __main__ - INFO - [FAST] Analysis completed in 2.72s
2025-06-08 19:21:12,940 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:12,942 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:12,942 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $89 < $8,000
2025-06-08 19:21:12,943 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:12,944 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:12,970 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:12,970 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:14,515 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:14,515 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:15,285 - __main__ - INFO - [FAST] Analysis completed in 2.34s
2025-06-08 19:21:15,288 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:15,289 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:15,290 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:15,290 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:16,407 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:16,438 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:16,438 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:18,056 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:18,057 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:18,848 - __main__ - INFO - [FAST] Analysis completed in 2.44s
2025-06-08 19:21:18,850 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:18,851 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:18,852 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:19,953 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:19,975 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:19,976 - __main__ - INFO - DexScreener data available - Price: $4.299e-06, Volume 24h: $19,081
2025-06-08 19:21:21,473 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.092855 SOL
2025-06-08 19:21:21,473 - __main__ - INFO - Using calculated liquidity: $27 (x1.95)
2025-06-08 19:21:22,230 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:21:22,233 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:22,234 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:22,234 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $27 < $8,000
2025-06-08 19:21:22,237 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:22,257 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:22,258 - __main__ - INFO - DexScreener data available - Price: $4.299e-06, Volume 24h: $19,081
2025-06-08 19:21:23,775 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.092855 SOL
2025-06-08 19:21:23,776 - __main__ - INFO - Using calculated liquidity: $27 (x1.95)
2025-06-08 19:21:24,561 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:21:24,563 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:24,564 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:24,565 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $27 < $8,000
2025-06-08 19:21:24,566 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:24,566 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:24,589 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:24,590 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:26,043 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:26,044 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:26,782 - __main__ - INFO - [FAST] Analysis completed in 2.21s
2025-06-08 19:21:26,784 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:26,785 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:26,786 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:26,786 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:27,875 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:28,186 - __main__ - INFO - Fetched fresh SOL price: $150.1 (attempt 1)
2025-06-08 19:21:28,187 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:28,187 - __main__ - INFO - DexScreener data available - Price: $4.249e-06, Volume 24h: $16,545
2025-06-08 19:21:30,228 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:30,229 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:30,982 - __main__ - INFO - [FAST] Analysis completed in 3.11s
2025-06-08 19:21:30,984 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:31,229 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:31,230 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:31,797 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:32,117 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:32,118 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:21:33,608 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:33,608 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:34,379 - __main__ - INFO - [FAST] Analysis completed in 2.58s
2025-06-08 19:21:34,381 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:34,382 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:34,382 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:34,383 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:35,594 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:35,656 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:35,656 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:21:37,214 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:37,214 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:37,934 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:21:38,191 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:38,192 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:38,192 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:39,282 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:39,306 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:39,307 - __main__ - INFO - DexScreener data available - Price: $4.299e-06, Volume 24h: $19,081
2025-06-08 19:21:40,773 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:21:41,774 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:21:42,533 - __main__ - INFO - [FAST] Analysis completed in 3.25s
2025-06-08 19:21:42,535 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:42,536 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:42,536 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:21:42,539 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:42,983 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:42,983 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:21:44,490 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:21:44,490 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:21:45,259 - __main__ - INFO - [FAST] Analysis completed in 2.72s
2025-06-08 19:21:45,261 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:45,262 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:45,263 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:21:45,263 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:45,264 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:45,287 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:45,287 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:21:46,843 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:46,844 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:47,588 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:21:47,591 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:47,592 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:47,592 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:47,593 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:48,703 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:48,725 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:48,726 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:21:50,220 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:50,221 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:50,947 - __main__ - INFO - [FAST] Analysis completed in 2.24s
2025-06-08 19:21:50,949 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:50,950 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:50,951 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:52,048 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:52,073 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:52,074 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:21:53,610 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:21:53,611 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:21:54,370 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:21:54,372 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:54,373 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:54,374 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:21:54,377 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:54,403 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:54,404 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:21:55,891 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:21:55,892 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:21:56,628 - __main__ - INFO - [FAST] Analysis completed in 2.25s
2025-06-08 19:21:56,631 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:56,632 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:56,632 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:21:56,633 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:21:56,633 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:56,669 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:21:56,670 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:21:58,136 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:21:58,137 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:21:58,864 - __main__ - INFO - [FAST] Analysis completed in 2.23s
2025-06-08 19:21:58,866 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:21:58,867 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:58,867 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:21:58,868 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:21:59,605 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:22:00,427 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:00,428 - __main__ - INFO - DexScreener data available - Price: $4.471e-05, Volume 24h: $62,569
2025-06-08 19:22:01,949 - __main__ - INFO - Found pool J7hJQkJ4... with 62.730091 SOL
2025-06-08 19:22:01,950 - __main__ - INFO - Using calculated liquidity: $18,361 (x1.95)
2025-06-08 19:22:02,719 - __main__ - INFO - [FAST] Analysis completed in 3.11s
2025-06-08 19:22:02,723 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:02,724 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:22:02,724 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $18,361, MC: $44,718, Whale: 23.7%
2025-06-08 19:22:02,725 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00004471, MC=$44,718, Liq=$18,361
2025-06-08 19:22:02,731 - execution_queue - INFO - Queued buy order for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with priority 5
2025-06-08 19:22:02,805 - execution_queue - INFO - Worker 3 executing buy for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump (Event ID: buy_6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump_1749390722.7288988)
2025-06-08 19:22:02,914 - trade_executor - INFO - Added buy_6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump_1749390722.7288988 to notified_buy_event_ids. Current size: 6
2025-06-08 19:22:02,915 - state_manager - INFO - [MONEY] BUY FEES (SIM/REAL): Buy tip: 0.0050 SOL, Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:22:02,916 - state_manager - INFO - [MONEY] TOTAL BUY FEES: 0.0180 SOL
2025-06-08 19:22:02,916 - state_manager - INFO - Opened position for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: 0.4000 SOL + 0.0180 SOL fees @ 0.00004471, Tokens: 8946.5444
2025-06-08 19:22:02,917 - state_manager - INFO - Available SOL after position + fees: 0.4154
2025-06-08 19:22:02,918 - trade_executor - INFO - Position opened in state manager for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with 0.4000 SOL at $0.00004471
2025-06-08 19:22:02,919 - execution_queue - INFO - Worker 3 successfully executed buy for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump in 0.11s
2025-06-08 19:22:03,876 - simulation_logger - INFO - Registering new position for monitoring: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:22:03,878 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (1.2s ago)
2025-06-08 19:22:03,879 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (1.2s ago)
2025-06-08 19:22:03,881 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:04,233 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:04,233 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:05,778 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:22:05,797 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:22:06,662 - __main__ - INFO - [FAST] Analysis completed in 2.78s
2025-06-08 19:22:06,700 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:06,701 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:06,701 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:22:06,702 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:07,907 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:07,930 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:07,931 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:09,387 - __main__ - INFO - Found pool Bs1EcrUj... with 0.120304 SOL
2025-06-08 19:22:09,387 - __main__ - INFO - Using calculated liquidity: $35 (x1.95)
2025-06-08 19:22:10,170 - __main__ - INFO - [FAST] Analysis completed in 2.26s
2025-06-08 19:22:10,174 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:10,175 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:10,175 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $35 < $8,000
2025-06-08 19:22:11,267 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:11,289 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:11,289 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:12,800 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:12,801 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:13,629 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:22:13,632 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:13,633 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:13,633 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:13,635 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:14,053 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:14,054 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:15,495 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:15,496 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:16,355 - __main__ - INFO - [FAST] Analysis completed in 2.72s
2025-06-08 19:22:16,359 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:16,360 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:16,362 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:16,363 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:16,364 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:16,388 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:16,388 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:17,901 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:17,901 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:18,670 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:22:18,673 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:18,674 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:18,675 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:18,675 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:19,876 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:19,902 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:19,903 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:21,435 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:21,436 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:22,192 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:22:22,195 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:22,195 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:22,196 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:23,283 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:23,305 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:23,306 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:24,759 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:24,760 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:25,525 - __main__ - INFO - [FAST] Analysis completed in 2.24s
2025-06-08 19:22:25,529 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:25,530 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:25,531 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:25,533 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:25,559 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:25,560 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:27,037 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:27,038 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:27,793 - __main__ - INFO - [FAST] Analysis completed in 2.26s
2025-06-08 19:22:27,795 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:27,796 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:27,796 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:27,797 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:27,798 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:27,824 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:27,825 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:29,305 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:29,306 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:30,080 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:22:30,083 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:30,083 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:30,084 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:30,085 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:31,283 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:31,580 - __main__ - INFO - Fetched fresh SOL price: $150.1 (attempt 1)
2025-06-08 19:22:31,581 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:31,581 - __main__ - INFO - DexScreener data available - Price: $4.245e-06, Volume 24h: $16,546
2025-06-08 19:22:33,060 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:33,060 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:33,813 - __main__ - INFO - [FAST] Analysis completed in 2.53s
2025-06-08 19:22:33,816 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:33,816 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:33,817 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:34,923 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:34,961 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:34,962 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:36,831 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:36,832 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:37,595 - __main__ - INFO - [FAST] Analysis completed in 2.67s
2025-06-08 19:22:37,597 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:37,598 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:37,599 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:37,601 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:37,630 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:37,631 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:39,160 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:39,161 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:39,924 - __main__ - INFO - [FAST] Analysis completed in 2.32s
2025-06-08 19:22:39,926 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:39,927 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:39,928 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:39,928 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:39,929 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:40,287 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:40,288 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:22:41,834 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:41,835 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:42,603 - __main__ - INFO - [FAST] Analysis completed in 2.67s
2025-06-08 19:22:42,605 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:42,606 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:42,606 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:42,607 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:43,814 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:43,840 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:43,841 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:22:45,368 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:45,369 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:46,164 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:22:46,166 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:46,167 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:46,168 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:47,267 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:47,691 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:47,691 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:49,200 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:49,201 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:49,978 - __main__ - INFO - [FAST] Analysis completed in 2.71s
2025-06-08 19:22:49,982 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:49,983 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:49,983 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:49,985 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:50,009 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:50,009 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:22:51,556 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:22:51,557 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:22:52,337 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:22:52,339 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:52,340 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:52,341 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:22:52,342 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:22:52,342 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:52,370 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:52,371 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:22:53,862 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:53,863 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:55,302 - __main__ - INFO - [FAST] Analysis completed in 2.96s
2025-06-08 19:22:55,304 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:55,305 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:55,306 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:22:55,307 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:56,518 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:56,540 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:22:56,541 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:22:58,038 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:22:58,038 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:22:58,794 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:22:58,796 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:22:58,797 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:22:58,798 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:00,190 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:00,210 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:00,211 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:23:01,714 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:23:01,715 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:23:02,462 - __main__ - INFO - [FAST] Analysis completed in 2.27s
2025-06-08 19:23:02,464 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:02,465 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:02,466 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:23:02,468 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:02,500 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:02,501 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:23:03,964 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:23:03,964 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:23:04,763 - __main__ - INFO - [FAST] Analysis completed in 2.29s
2025-06-08 19:23:04,765 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:04,766 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:04,767 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:23:04,768 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:04,768 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:04,792 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:04,793 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:06,344 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:06,345 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:07,127 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:23:07,130 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:07,131 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:07,132 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:07,132 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:08,347 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:08,368 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:08,369 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:09,945 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:09,945 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:10,694 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:23:10,697 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:10,698 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:10,699 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:11,799 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:11,823 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:11,824 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:23:13,382 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:23:13,383 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:23:14,115 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:23:14,117 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:14,118 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:14,119 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:23:14,121 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:14,145 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:14,146 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:23:15,638 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:23:15,639 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:23:16,417 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:23:16,419 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:16,420 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:16,421 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:23:16,422 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:23:16,423 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:16,776 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:16,776 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:18,318 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:18,318 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:19,113 - __main__ - INFO - [FAST] Analysis completed in 2.69s
2025-06-08 19:23:19,115 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:19,116 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:19,117 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:19,117 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:20,315 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:20,339 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:20,340 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:22,783 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:22,784 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:23,608 - __main__ - INFO - [FAST] Analysis completed in 3.29s
2025-06-08 19:23:23,610 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:23,611 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:23,611 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:24,128 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:24,386 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:24,387 - __main__ - INFO - DexScreener data available - Price: $4.03e-05, Volume 24h: $66,229
2025-06-08 19:23:25,887 - __main__ - INFO - Found pool J7hJQkJ4... with 43.811558 SOL
2025-06-08 19:23:25,887 - __main__ - INFO - Using calculated liquidity: $12,823 (x1.95)
2025-06-08 19:23:26,615 - __main__ - INFO - [FAST] Analysis completed in 2.49s
2025-06-08 19:23:26,617 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:26,618 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:26,619 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $12,823, MC: $40,310, Whale: 22.4%
2025-06-08 19:23:26,620 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00004030, MC=$40,310, Liq=$12,823
2025-06-08 19:23:26,622 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:26,648 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:26,649 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:28,184 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:28,185 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:28,954 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:23:28,957 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:28,958 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:28,958 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:28,959 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:30,050 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:30,073 - __main__ - INFO - Fetched data - SOL price: $150.1, Supply: 1,000,000,000
2025-06-08 19:23:30,074 - __main__ - INFO - DexScreener data available - Price: $4.03e-05, Volume 24h: $66,229
2025-06-08 19:23:31,636 - __main__ - INFO - Found pool J7hJQkJ4... with 43.925570 SOL
2025-06-08 19:23:31,637 - __main__ - INFO - Using calculated liquidity: $12,857 (x1.95)
2025-06-08 19:23:32,418 - __main__ - INFO - [FAST] Analysis completed in 2.37s
2025-06-08 19:23:32,420 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:32,421 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:32,421 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $12,857, MC: $40,310, Whale: 22.4%
2025-06-08 19:23:32,422 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00004030, MC=$40,310, Liq=$12,857
2025-06-08 19:23:33,628 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:33,909 - __main__ - INFO - Fetched fresh SOL price: $150.36 (attempt 1)
2025-06-08 19:23:33,910 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:33,910 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:35,448 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:35,448 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:36,166 - __main__ - INFO - [FAST] Analysis completed in 2.54s
2025-06-08 19:23:36,168 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:36,169 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:36,170 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:36,171 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (3.7s ago)
2025-06-08 19:23:36,172 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:36,195 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:36,196 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:37,704 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:37,705 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:38,456 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:23:38,458 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:38,459 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:38,460 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:38,461 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:39,769 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:39,792 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:39,793 - __main__ - INFO - DexScreener data available - Price: $4.03e-05, Volume 24h: $66,229
2025-06-08 19:23:41,272 - __main__ - INFO - Found pool J7hJQkJ4... with 46.413033 SOL
2025-06-08 19:23:41,273 - __main__ - INFO - Using calculated liquidity: $13,608 (x1.95)
2025-06-08 19:23:42,027 - __main__ - INFO - [FAST] Analysis completed in 2.26s
2025-06-08 19:23:42,029 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:42,030 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:42,030 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $13,608, MC: $40,310, Whale: 22.4%
2025-06-08 19:23:42,031 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00004030, MC=$40,310, Liq=$13,608
2025-06-08 19:23:43,238 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:43,262 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:43,263 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:44,820 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:44,820 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:45,603 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:23:45,607 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:45,608 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:45,609 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:45,610 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (3.6s ago)
2025-06-08 19:23:45,611 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:45,636 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:45,637 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:47,167 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:47,168 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:47,888 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:23:47,891 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:47,892 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:47,893 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:47,894 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:49,207 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:49,229 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:49,230 - __main__ - INFO - DexScreener data available - Price: $4.03e-05, Volume 24h: $66,229
2025-06-08 19:23:50,773 - __main__ - INFO - Found pool J7hJQkJ4... with 46.775505 SOL
2025-06-08 19:23:50,774 - __main__ - INFO - Using calculated liquidity: $13,715 (x1.95)
2025-06-08 19:23:51,560 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:23:51,563 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:51,564 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:51,565 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $13,715, MC: $40,310, Whale: 22.4%
2025-06-08 19:23:51,565 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00004030, MC=$40,310, Liq=$13,715
2025-06-08 19:23:52,769 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:53,308 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:53,309 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:54,790 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:54,791 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:55,556 - __main__ - INFO - [FAST] Analysis completed in 2.74s
2025-06-08 19:23:55,558 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:55,559 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:55,560 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:55,561 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (4.0s ago)
2025-06-08 19:23:55,562 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:55,583 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:55,584 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:23:57,538 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:23:57,538 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:23:58,309 - __main__ - INFO - [FAST] Analysis completed in 2.75s
2025-06-08 19:23:58,311 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:23:58,312 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:58,313 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:23:58,314 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:23:59,410 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:23:59,675 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:23:59,675 - __main__ - INFO - DexScreener data available - Price: $2.519e-05, Volume 24h: $70,325
2025-06-08 19:24:01,182 - __main__ - INFO - Found pool J7hJQkJ4... with 46.806134 SOL
2025-06-08 19:24:01,183 - __main__ - INFO - Using calculated liquidity: $13,724 (x1.95)
2025-06-08 19:24:01,985 - __main__ - INFO - [FAST] Analysis completed in 2.57s
2025-06-08 19:24:01,987 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:01,988 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:24:01,989 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $13,724, MC: $25,197, Whale: 22.4%
2025-06-08 19:24:01,990 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00002519, MC=$25,197, Liq=$13,724
2025-06-08 19:24:03,007 - execution_queue - INFO - Queued sell order for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with priority 0
2025-06-08 19:24:03,020 - execution_queue - WARNING - EMERGENCY SELL: Processing 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump immediately
2025-06-08 19:24:03,021 - execution_queue - INFO - Worker 3 executing sell for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump (Fraction: 100.0%, EventID: sell_6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump_Stop_Loss_hit_(-43.7pct_<=_-25.0pct)_1749390841)
2025-06-08 19:24:03,021 - trade_executor - INFO - SELL DEBUG: execute_sell called for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with token_amount=8946.544397226573, entry_price=4.471e-05, sell_fraction=1.0, EventID: sell_6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump_Stop_Loss_hit_(-43.7pct_<=_-25.0pct)_1749390841
2025-06-08 19:24:03,022 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=8946.544397226573 (token_amount=8946.544397226573, sell_fraction=1.0)
2025-06-08 19:24:03,081 - trade_executor - INFO - SELL PRICE LOGGING: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump - Entry: 0.00004471, Current: 0.00002519, Est. PnL: -43.66%
2025-06-08 19:24:03,144 - trade_executor - INFO - SELL DEBUG: Added sell_6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump_Stop_Loss_hit_(-43.7pct_<=_-25.0pct)_1749390841 to notified_sell_event_ids. Current size: 4
2025-06-08 19:24:03,145 - execution_queue - INFO - Worker 3 successfully executed sell for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump in 0.12s
2025-06-08 19:24:03,146 - state_manager - INFO - SELL DEBUG: Attempting to close position for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with exit_price=2.519e-05, reason=Stop Loss hit (-43.7% <= -25.0%), fraction=1.0
2025-06-08 19:24:03,147 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump', 'BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump', 'E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump', '8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump', '9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump', '6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump']
2025-06-08 19:24:03,148 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0023 SOL, Platform: 0.0023 SOL
2025-06-08 19:24:03,149 - state_manager - INFO - [MONEY] SOL received: 0.2254 gross - 0.0045 fees = 0.2209 net
2025-06-08 19:24:03,149 - state_manager - INFO - SELL for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Sold 8946.5444 tokens @ 0.000025. PnL: -0.2021 SOL
2025-06-08 19:24:03,149 - state_manager - INFO - Position 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump closed. Total Profit SOL: -0.0688, Wins: 2, Losses: 2
2025-06-08 19:24:03,150 - state_manager - INFO - Recalculated available SOL: 0.6312
2025-06-08 19:24:03,186 - state_manager - INFO - State saved successfully to sessions/trading_sim_session.session
2025-06-08 19:24:03,316 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:03,734 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:03,735 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:05,248 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:05,249 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:05,994 - __main__ - INFO - [FAST] Analysis completed in 2.68s
2025-06-08 19:24:05,996 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:05,997 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:05,998 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:05,998 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:05,999 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:06,025 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:06,026 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:07,572 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:07,572 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:08,283 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:24:08,287 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:08,288 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:08,289 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:08,289 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:08,290 - simulation_logger - INFO - Logging closed position: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:24:08,291 - simulation_logger - INFO - Logged closed position for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump with PNL: -43.66%
2025-06-08 19:24:08,293 - __main__ - INFO - SIGNAL CACHE HIT: Skipping 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump: Recently analyzed (6.3s ago)
2025-06-08 19:24:09,380 - __main__ - INFO - [FAST] Analyzing token: 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:24:09,404 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:09,405 - __main__ - INFO - DexScreener data available - Price: $2.519e-05, Volume 24h: $70,325
2025-06-08 19:24:10,952 - __main__ - INFO - Found pool J7hJQkJ4... with 48.230647 SOL
2025-06-08 19:24:10,952 - __main__ - INFO - Using calculated liquidity: $14,141 (x1.95)
2025-06-08 19:24:11,729 - __main__ - INFO - [FAST] Analysis completed in 2.35s
2025-06-08 19:24:11,731 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:11,732 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump
2025-06-08 19:24:11,733 - __main__ - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $14,141, MC: $25,197, Whale: 22.4%
2025-06-08 19:24:11,734 - __main__ - INFO - [SUCCESS] Fast analysis result: Price=$0.00002519, MC=$25,197, Liq=$14,141
2025-06-08 19:24:13,302 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:13,323 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:13,324 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:14,736 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:14,737 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:15,510 - __main__ - INFO - [FAST] Analysis completed in 2.21s
2025-06-08 19:24:15,512 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:15,513 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:15,514 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:15,515 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:16,614 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:16,636 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:16,637 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:18,628 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:18,629 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:19,377 - __main__ - INFO - [FAST] Analysis completed in 2.76s
2025-06-08 19:24:19,379 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:19,380 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:19,381 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:20,520 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:20,547 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:20,548 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:22,013 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:22,014 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:22,741 - __main__ - INFO - [FAST] Analysis completed in 2.22s
2025-06-08 19:24:22,743 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:22,744 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:22,744 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:22,745 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:23,833 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:23,855 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:23,856 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:25,283 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:25,283 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:26,021 - __main__ - INFO - [FAST] Analysis completed in 2.19s
2025-06-08 19:24:26,024 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:26,025 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:26,025 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:27,224 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:27,594 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:27,594 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:29,098 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:29,099 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:29,883 - __main__ - INFO - [FAST] Analysis completed in 2.66s
2025-06-08 19:24:29,885 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:29,886 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:29,887 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:29,888 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:29,911 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:29,911 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:31,420 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:31,421 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:32,219 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:24:32,222 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:32,223 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:32,224 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:32,225 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:33,599 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:33,625 - __main__ - INFO - Fetched data - SOL price: $150.36, Supply: 1,000,000,000
2025-06-08 19:24:33,625 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:35,104 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:35,105 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:35,865 - __main__ - INFO - [FAST] Analysis completed in 2.27s
2025-06-08 19:24:35,868 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:35,869 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:35,870 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:36,959 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:37,250 - __main__ - INFO - Fetched fresh SOL price: $150.37 (attempt 1)
2025-06-08 19:24:37,376 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:37,377 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:38,821 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:38,821 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:39,541 - __main__ - INFO - [FAST] Analysis completed in 2.58s
2025-06-08 19:24:39,543 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:39,545 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:39,545 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:39,549 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:39,574 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:39,575 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:41,129 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:41,130 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:41,887 - __main__ - INFO - [FAST] Analysis completed in 2.34s
2025-06-08 19:24:41,889 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:41,890 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:41,891 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:41,892 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:41,893 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:41,916 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:41,917 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:43,367 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:43,367 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:44,123 - __main__ - INFO - [FAST] Analysis completed in 2.23s
2025-06-08 19:24:44,126 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:44,127 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:44,128 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:44,129 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:45,225 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:45,256 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:45,257 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:46,705 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:46,706 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:47,506 - __main__ - INFO - [FAST] Analysis completed in 2.28s
2025-06-08 19:24:47,509 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:47,510 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:47,511 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:48,600 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:48,632 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:48,632 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:50,564 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:50,565 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:51,320 - __main__ - INFO - [FAST] Analysis completed in 2.72s
2025-06-08 19:24:51,323 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:51,325 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:51,326 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:51,330 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:51,353 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:51,354 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:24:52,836 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:24:52,836 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:24:53,570 - __main__ - INFO - [FAST] Analysis completed in 2.24s
2025-06-08 19:24:53,574 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:53,575 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:53,576 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:24:53,576 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:24:53,577 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:53,604 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:53,605 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:55,098 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:55,099 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:55,873 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:24:55,876 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:55,878 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:55,879 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:24:55,879 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:56,975 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:57,000 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:24:57,001 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:24:58,552 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:24:58,553 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:24:59,338 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:24:59,341 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:24:59,342 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:24:59,343 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:25:00,428 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:00,453 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:00,454 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:01,951 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:01,951 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:02,725 - __main__ - INFO - [FAST] Analysis completed in 2.30s
2025-06-08 19:25:02,728 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:02,729 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:02,730 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:02,733 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:02,756 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:02,757 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:04,253 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:04,254 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:05,063 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:25:05,066 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:05,067 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:05,067 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:05,068 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:05,069 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:05,419 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:05,420 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:25:06,982 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:25:06,983 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:25:07,727 - __main__ - INFO - [FAST] Analysis completed in 2.66s
2025-06-08 19:25:07,730 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:07,731 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:07,732 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:25:07,733 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:07,848 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:07,874 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:07,875 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:25:09,381 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:25:09,382 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:25:10,135 - __main__ - INFO - [FAST] Analysis completed in 2.29s
2025-06-08 19:25:10,137 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:10,138 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:10,139 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:25:10,143 - trade_executor - INFO - SELL DEBUG: execute_sell called for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump with token_amount=26420.07926023778, entry_price=1.514e-05, sell_fraction=1.0, EventID: None
2025-06-08 19:25:10,143 - trade_executor - WARNING - execute_sell called for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump without an event_id. Duplicate action prevention may not be effective.
2025-06-08 19:25:10,144 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=26420.07926023778 (token_amount=26420.07926023778, sell_fraction=1.0)
2025-06-08 19:25:10,226 - trade_executor - INFO - SELL PRICE LOGGING: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump - Entry: 0.00001514, Current: 0.00000421, Est. PnL: -72.19%
2025-06-08 19:25:10,286 - state_manager - INFO - SELL DEBUG: Attempting to close position for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump with exit_price=1.514e-05, reason=manual_sell_all, fraction=1.0
2025-06-08 19:25:10,287 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump', 'BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump', 'E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump', '8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump', '9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump', '6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump']
2025-06-08 19:25:10,288 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:25:10,288 - state_manager - INFO - [MONEY] SOL received: 0.4000 gross - 0.0080 fees = 0.3920 net
2025-06-08 19:25:10,289 - state_manager - INFO - SELL for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump: Sold 26420.0793 tokens @ 0.000015. PnL: -0.0310 SOL
2025-06-08 19:25:10,290 - state_manager - INFO - Position 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump closed. Total Profit SOL: -0.0998, Wins: 2, Losses: 3
2025-06-08 19:25:10,291 - state_manager - INFO - Recalculated available SOL: 1.0002
2025-06-08 19:25:10,292 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:10,713 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:10,713 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:12,240 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:12,241 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:13,004 - __main__ - INFO - [FAST] Analysis completed in 2.71s
2025-06-08 19:25:13,008 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:13,009 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:13,010 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:13,013 - trade_executor - INFO - SELL DEBUG: execute_sell called for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump with token_amount=23041.************, entry_price=1.736e-05, sell_fraction=1.0, EventID: None
2025-06-08 19:25:13,014 - trade_executor - WARNING - execute_sell called for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump without an event_id. Duplicate action prevention may not be effective.
2025-06-08 19:25:13,014 - trade_executor - INFO - SELL DEBUG: Calculated amount_to_sell_tokens=23041.************ (token_amount=23041.************, sell_fraction=1.0)
2025-06-08 19:25:13,019 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:13,043 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:13,044 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:25:14,549 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:25:14,550 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:25:15,355 - __main__ - INFO - [FAST] Analysis completed in 2.34s
2025-06-08 19:25:15,358 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:15,359 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:15,359 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:25:15,360 - simulation_logger - WARNING - Could not get analysis data for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:15,361 - simulation_logger - INFO - Logging closed position: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:15,362 - __main__ - INFO - [FAST] Analyzing token: 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:15,386 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:15,386 - __main__ - INFO - DexScreener data available - Price: $4.21e-06, Volume 24h: $16,559
2025-06-08 19:25:17,516 - __main__ - INFO - Found pool Bs1EcrUj... with 0.029994 SOL
2025-06-08 19:25:17,517 - __main__ - INFO - Using calculated liquidity: $9 (x1.95)
2025-06-08 19:25:18,278 - __main__ - INFO - [FAST] Analysis completed in 2.91s
2025-06-08 19:25:18,280 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:18,281 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump
2025-06-08 19:25:18,281 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $9 < $8,000
2025-06-08 19:25:18,283 - simulation_logger - INFO - Logged closed position for 8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump with PNL: 0.00%
2025-06-08 19:25:18,867 - trade_executor - INFO - SELL PRICE LOGGING: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump - Entry: 0.00001736, Current: 0.00000423, Est. PnL: -75.62%
2025-06-08 19:25:18,918 - state_manager - INFO - SELL DEBUG: Attempting to close position for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump with exit_price=1.736e-05, reason=manual_sell_all, fraction=1.0
2025-06-08 19:25:18,918 - state_manager - INFO - SELL DEBUG: Current positions: ['GLXwjjxjaxCYc9W4bcbqUUqcVB7pE1PXzT2MRpQ1pump', 'BSuxYxyJNC82tjazDEj1GWo2Z9VsouWm4epPLSxCpump', 'E45CrFRhgapxCnd8BX9XE1pNdNGXniuqugCyhxcupump', '8r4zqDqMor5aoZjUGCFFoevGdi63YEhRuq6qATGepump', '9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump', '6xeTcx9yeFaktJo7ap6czBVT546UxhZ1vyF4zQnKpump']
2025-06-08 19:25:18,919 - state_manager - INFO - [MONEY] SELL FEES (SIM/REAL): Gas: 0.0050 SOL, Handling: 0.0040 SOL, Platform: 0.0040 SOL
2025-06-08 19:25:18,920 - state_manager - INFO - [MONEY] SOL received: 0.4000 gross - 0.0080 fees = 0.3920 net
2025-06-08 19:25:18,921 - state_manager - INFO - SELL for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump: Sold 23041.4747 tokens @ 0.000017. PnL: -0.0310 SOL
2025-06-08 19:25:18,922 - state_manager - INFO - Position 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump closed. Total Profit SOL: -0.1308, Wins: 2, Losses: 4
2025-06-08 19:25:18,922 - state_manager - INFO - Recalculated available SOL: 1.3692
2025-06-08 19:25:19,476 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:19,498 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:19,499 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:21,036 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:21,037 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:21,810 - __main__ - INFO - [FAST] Analysis completed in 2.33s
2025-06-08 19:25:21,812 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:21,813 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:21,814 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:23,289 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:23,313 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:23,314 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:24,806 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:24,807 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:25,650 - __main__ - INFO - [FAST] Analysis completed in 2.36s
2025-06-08 19:25:25,652 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:25,654 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:25,654 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:25,655 - simulation_logger - WARNING - Could not get analysis data for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:25,656 - simulation_logger - INFO - Logging closed position: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:25,657 - __main__ - INFO - [FAST] Analyzing token: 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:25,680 - __main__ - INFO - Fetched data - SOL price: $150.37, Supply: 1,000,000,000
2025-06-08 19:25:25,681 - __main__ - INFO - DexScreener data available - Price: $4.233e-06, Volume 24h: $19,114
2025-06-08 19:25:27,207 - __main__ - INFO - Found pool 9r8sFQQ9... with 0.097859 SOL
2025-06-08 19:25:27,207 - __main__ - INFO - Using calculated liquidity: $29 (x1.95)
2025-06-08 19:25:27,970 - __main__ - INFO - [FAST] Analysis completed in 2.31s
2025-06-08 19:25:27,972 - config_manager - INFO - Loading config from: C:\Users\<USER>\Documents\Another one test working - Copy\finalconfig.json
2025-06-08 19:25:27,974 - __main__ - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump
2025-06-08 19:25:27,974 - __main__ - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $29 < $8,000
2025-06-08 19:25:27,976 - simulation_logger - INFO - Logged closed position for 9HG7gaqpnhzKyawaMAWZKZuP1zr5ednSeigaJEmrpump with PNL: 0.00%
