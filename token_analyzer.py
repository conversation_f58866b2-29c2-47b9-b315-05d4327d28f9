import asyncio
import aiohttp
import json
import logging
import random  # Used for backoff jitter in API retry logic
import re
import time
from typing import Dict, Any, List, Set
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

# Import RateLimiter from api_clients to avoid duplication
from api_clients import RateLimiter



class TokenAnalyzer:
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.session = None
        self.rate_limiter = RateLimiter()
        self.sessions: Dict[str, aiohttp.ClientSession] = {}
        self.price_data = {}  # token -> price
        self.last_price_update = {}  # token -> timestamp
        self.volume_data = {}  # token -> volume
        self.price_history = {}  # token -> list of prices
        self.volume_history = {}  # token -> list of volumes
        self.max_history_length = 100  # Maximum number of historical data points to keep
        self.watchlist_tokens: Set[str] = set()
        # Updated API priority with DexScreener as primary
        self.api_priority = ['dexscreener']

        # Hybrid API client removed
        self.hybrid_client = None

        # Moralis client removed
        # Get timeout from config, fallback to optimized defaults
        config_timeout = config_manager.get('trading_settings', 'api_request_timeout', default=1.0)
        self.api_timeouts = {
            'dexscreener': config_timeout  # Use timeout from finalconfig.json
        }

        # Rate limiting for DexScreener
        self.last_dexscreener_call = 0
        self.dexscreener_spacing = config_manager.get('trading_settings', 'api_request_spacing_ms', default=1100) / 1000
        self.api_retries = {
            'dexscreener': 3
        }
        self.api_retry_delays = {
            'dexscreener': 0.5  # Reduced from 1.0 to 0.5 for faster retries
        }
        # Cache settings - set to 0 seconds for all providers to ensure fresh data for volatile meme coins
        self.cache_duration = {
            'dexscreener': 0  # No caching for DexScreener data - always get fresh data
        }
        # Load parameters directly from finalconfig.json
        try:
            # Get trading settings from config
            trading_settings = self.config_manager.get_section('trading_settings')

            # Get liquidity thresholds from trading_settings
            liquidity_thresholds = trading_settings.get('liquidity_thresholds', {})
            self.min_liquidity_sol = liquidity_thresholds.get('min_sol', 20.0)

            # FIXED: Get rug protection parameters from centralized location
            rug_protection = trading_settings.get('rug_protection', {})
            self.min_liquidity_usd = rug_protection.get('min_liquidity_usd', 8000.0)  # SURGICAL FIX: Updated default
            self.min_vol_5m_usd = rug_protection.get('min_volume_5m_usd', 10000.0)
            self.min_marketcap_usd = rug_protection.get('min_fdv_usd', 10000.0)  # Use FDV as market cap proxy
            self.min_vol_1h_usd = self.min_vol_5m_usd * 12  # Estimate 1h volume from 5m

            # Other parameters with defaults
            self.min_fdv_change_5m_pct = trading_settings.get('fdv_surge_threshold_percent', 150.0)
            self.min_holder_count = 30  # Default
            self.max_bundled_pct = 60.0  # Default

            logger.info(f"✅ CENTRALIZED RUG PROTECTION: Loaded parameters from rug_protection section: "
                       f"min_liquidity_sol={self.min_liquidity_sol}, "
                       f"min_liquidity_usd={self.min_liquidity_usd}, "
                       f"min_marketcap_usd={self.min_marketcap_usd}, "
                       f"min_vol_5m_usd={self.min_vol_5m_usd}")

        except Exception as e:
            logger.error(f"Error loading config, using default values: {e}")
            # Default values if config fails
            self.min_liquidity_sol = 20.0
            self.min_liquidity_usd = 8000.0  # SURGICAL FIX: Updated default
            self.min_marketcap_usd = 10000.0
            self.min_vol_5m_usd = 10000.0
            self.min_vol_1h_usd = self.min_vol_5m_usd * 12
            self.min_fdv_change_5m_pct = 150.0
            self.min_holder_count = 30
            self.max_bundled_pct = 60.0

        # Time-based parameters - read from rug_protection section
        self.min_pair_age_mins = rug_protection.get('min_token_age_minutes', 0)
        self.max_pair_age_mins = float('inf')  # Disabled - volume/liquidity/mc/txn activity matters more than age

        # SOL price tracking
        self.sol_usd_price = 0  # Initialize to 0 instead of None to avoid comparison issues
        self.sol_price_last_update = 0

    def get_cached_token_data(self, token_address: str) -> Dict[str, Any]:
        """
        Get cached token data for a token address.

        Args:
            token_address: The token address to get cached data for

        Returns:
            Dict containing cached token data or empty dict if not found
        """
        try:
            # Check if we have cached data for this token
            cache_key = f"dexscreener_{token_address}"
            if cache_key in self.rate_limiter.cache:
                cached_item = self.rate_limiter.cache[cache_key]
                logger.debug(f"Found cached token data for {token_address}")
                return cached_item['data']

            # Try other cache keys if the primary one isn't found
            for key in self.rate_limiter.cache:
                if token_address in key:
                    cached_item = self.rate_limiter.cache[key]
                    logger.debug(f"Found cached token data for {token_address} under key {key}")
                    return cached_item['data']

            logger.debug(f"No cached token data found for {token_address}")
            return {}
        except Exception as e:
            logger.error(f"Error getting cached token data for {token_address}: {e}")
            return {}

    def get_cached_token_details(self, token_address: str) -> Dict[str, Any]:
        """
        Get cached token details (processed data) for a token address.

        Args:
            token_address: The token address to get cached details for

        Returns:
            Dict containing processed token details or empty dict if not found
        """
        try:
            # This would return processed analysis results if we had them cached
            # For now, just return basic data from the raw cache
            raw_data = self.get_cached_token_data(token_address)

            if not raw_data:
                return {}

            # Extract basic details from raw data
            result = {
                'name': 'Unknown',
                'symbol': 'Unknown',
                'price': 0.0,
                'liquidity_usd': 0.0,
                'volume_24h': 0.0,
                'market_cap': 0.0
            }

            # Try to extract from dexscreener data
            if 'pairs' in raw_data and raw_data['pairs'] and len(raw_data['pairs']) > 0:
                pair = raw_data['pairs'][0]

                # Basic token info
                result['name'] = pair.get('baseToken', {}).get('name', 'Unknown')
                result['symbol'] = pair.get('baseToken', {}).get('symbol', 'Unknown')

                # Price and metrics
                try:
                    result['price'] = float(pair.get('priceUsd', 0))
                except (ValueError, TypeError):
                    pass

                try:
                    result['liquidity_usd'] = float(pair.get('liquidity', {}).get('usd', 0))
                except (ValueError, TypeError):
                    pass

                try:
                    result['volume_24h'] = float(pair.get('volume', {}).get('h24', 0))
                except (ValueError, TypeError):
                    pass

                try:
                    result['market_cap'] = float(pair.get('fdv', 0))
                except (ValueError, TypeError):
                    pass

            return result
        except Exception as e:
            logger.error(f"Error getting cached token details for {token_address}: {e}")
            return {}

    async def init_sessions(self):
        """Initialize API sessions with proper connection pooling"""
        try:
            if not self.session or self.session.closed:
                # Create a TCP connector with proper settings for connection pooling
                connector = aiohttp.TCPConnector(
                    limit=10,  # Limit concurrent connections
                    ttl_dns_cache=300,  # Cache DNS results for 5 minutes
                    enable_cleanup_closed=True,  # Clean up closed connections
                    force_close=False  # Keep connections alive
                )

                # Create a session with the connector
                self.session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=aiohttp.ClientTimeout(total=30)  # Default timeout
                )

                logger.info("Initialized new aiohttp session with connection pooling")

                # Initialize sessions dictionary with the same session for all APIs
                for source in self.api_priority:
                    self.sessions[source] = self.session
            else:
                logger.debug("Using existing aiohttp session")
        except Exception as e:
            logger.error(f"Error initializing sessions: {e}", exc_info=True)
            # If we fail to create a session, we'll try again on the next call

    async def analyze(self, token_address: str, **kwargs) -> Dict[str, Any]:
        """
        Analyze token with multiple data sources and fallbacks

        Args:
            token_address: The token address to analyze
            **kwargs: Additional parameters:
                - force_fresh: Whether to bypass cache and force fresh data (optional, default=False)
                - skip_notifications: Whether to skip sending notifications (optional, default=False)
                - signal_message: Optional message from signal to extract metrics from
                - is_active_position: Whether this is an active position
                - extracted_metrics: Metrics extracted from the signal message
                - is_gmgn_channel: Whether this token is from the GMGN channel (optional)
                - source_channel: Source channel of the signal (optional)
                - source_channel_id: ID of the channel where the signal originated (optional)
                - signal_source: Source of the signal (optional)

                Note: The following parameters are documented for future use but are not currently used:
                - signal_timestamp: Timestamp when the signal was received
                - signal_confidence: Confidence score of the signal
        """
        # Extract parameters we need, ignore any we don't need
        is_active_position = kwargs.get('is_active_position', False)
        signal_message = kwargs.get('signal_message', '')
        extracted_metrics = kwargs.get('extracted_metrics', {})
        force_fresh = kwargs.get('force_fresh', False)
        skip_notifications = kwargs.get('skip_notifications', False)

        logger.info(f"Analyzing token {token_address} with force_fresh={force_fresh}")

        # Accept is_gmgn_channel directly if provided
        is_gmgn_channel = kwargs.get('is_gmgn_channel', False)

        # If is_gmgn_channel is not provided, check source_channel_id and source_channel
        if not is_gmgn_channel:
            # Special handling for GMGN channel (ID: -1001938040677)
            source_channel_id = kwargs.get('source_channel_id')
            source_channel = kwargs.get('source_channel', '')

            # Check if this is from GMGN channel - multiple detection methods
            if (source_channel_id == -1001938040677 or
                (source_channel and 'GMGN' in str(source_channel)) or
                (source_channel and 'solana signal alert - gmgn' in str(source_channel).lower())):
                is_gmgn_channel = True

        # Also check if source_type is 'gmgn' in the kwargs
        if not is_gmgn_channel and kwargs.get('source_type') == 'gmgn':
            is_gmgn_channel = True

        if is_gmgn_channel:
            logger.info(f"GMGN channel detected for token {token_address}")

        # Remove parameters that might cause errors in downstream calls
        clean_kwargs = {
            'is_active_position': is_active_position,
            'signal_message': signal_message,
            'extracted_metrics': extracted_metrics,
            'is_gmgn_channel': is_gmgn_channel,
            'force_fresh': force_fresh,
            'skip_notifications': skip_notifications
        }

        # Also include source_type if available (for GMGN channel detection)
        if 'source_type' in kwargs:
            clean_kwargs['source_type'] = kwargs['source_type']

        if is_gmgn_channel:
            logger.info(f"GMGN CHANNEL DETECTED: Special handling for token {token_address}")

        # Get signal message for potential use in metrics extraction
        # This is used by the _extract_metrics_from_message method if needed
        signal_message = kwargs.get('signal_message', '')

        # Use the clean_kwargs for downstream calls
        kwargs = clean_kwargs

        analysis_result = {
            "exists": False,
            "price": 0.0,
            "price_source": None,
            "confidence": 0.0,
            "sentiment": 0.0,
            "reason": "",
            "raw_data": {},
            "market_cap": 0.0,
            "liquidity_usd": 0.0,
            "liquidity_sol": 0.0,
            "volume_5m": 0.0,
            "volume_1h": 0.0,
            "volume_24h": 0.0,
            "pair_age_minutes": 0,
            "tx_count_24h": 0,
            "fdv": 0.0,
            "fdv_change_5m": 0.0,
            "holder_count": 0,
            "bundled_pct": 0.0,
            "name": "Unknown",
            "symbol": "Unknown"
        }

        # Add signal message to analysis result if provided
        if 'signal_message' in kwargs and kwargs['signal_message']:
            analysis_result['signal_message'] = kwargs['signal_message']

        try:
            # Initialize sessions if needed
            await self.init_sessions()

            if not self.session or self.session.closed:
                logger.error("Failed to initialize session for token analysis")
                analysis_result['reason'] = "Session initialization failed"
                return analysis_result

            # Simplified direct API call - no complex async task management
            try:
                logger.info(f"Fetching DexScreener data for {token_address}")
                api_data = await self.get_dex_screener_data(token_address, force_fresh=True, is_gmgn_channel=is_gmgn_channel)
                used_source = 'dexscreener'

                if api_data and api_data.get('exists', False):
                    logger.info(f"Successfully fetched DexScreener data for {token_address}")
                    analysis_result['raw_data']['dexscreener'] = api_data
                else:
                    logger.warning(f"DexScreener returned no valid data for {token_address}")
                    api_data = None
                    used_source = None

            except Exception as e:
                logger.error(f"Error fetching DexScreener data: {e}")
                api_data = None
                used_source = None

            # Hybrid API removed

            # If no API data was successfully fetched from any source
            if not api_data or not used_source:
                    logger.warning(f"Failed to fetch valid data from any API source for {token_address}")
                    analysis_result['reason'] = "No valid API data"
                    # Ensure confidence is 0 if no API data
                    analysis_result['confidence'] = self._calculate_confidence({}, extracted_metrics if extracted_metrics else {})
                    # Add skip_notifications flag to the result so it can be used by the bot_controller
                    analysis_result['skip_notifications'] = skip_notifications
                    logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (API data failed)")
                    return analysis_result

            # Now, api_data holds the data from the chosen source (e.g., DexScreener)
            # And extracted_metrics (from signal_handler) are available in the kwargs scope

            # Special handling for GMGN channel tokens using extracted_metrics
            # This uses the 'extracted_metrics' from kwargs directly
            if is_gmgn_channel and extracted_metrics:
                if 'liquidity' in extracted_metrics:
                    message_liquidity = extracted_metrics.get('liquidity')
                    logger.info(f"GMGN CHANNEL: Using liquidity from message: {message_liquidity} for {token_address}")
                    # Update the api_data (which is a reference to the chosen raw data)
                    api_data['liquidity'] = message_liquidity
                    # Also update the specific raw_data entry
                    if used_source and used_source in analysis_result['raw_data']:
                        analysis_result['raw_data'][used_source]['liquidity'] = message_liquidity


            # Early rejection based on liquidity (SOL) - using api_data
            # Note: The key for liquidity in api_data might be 'liquidity_sol' or just 'liquidity' (USD)
            # Assuming 'liquidity_sol' is populated by get_dex_screener_data if applicable
            liquidity_sol_from_api = api_data.get('liquidity_sol', api_data.get('liquidity', 0) / self.sol_usd_price if self.sol_usd_price else 0)


            # CRITICAL FIX: Check BOTH SOL and USD liquidity - token passes if EITHER is sufficient
            # Skip liquidity check for GMGN channel tokens
            if not is_gmgn_channel:
                # Get USD liquidity for comparison
                liquidity_usd_temp = api_data.get('liquidity', 0)

                # Token fails ONLY if BOTH SOL and USD liquidity are insufficient
                sol_liquidity_insufficient = liquidity_sol_from_api < self.min_liquidity_sol
                usd_liquidity_insufficient = liquidity_usd_temp < self.min_liquidity_usd

                if sol_liquidity_insufficient and usd_liquidity_insufficient:
                    reason = f"Low liquidity: {liquidity_sol_from_api:.2f} SOL < {self.min_liquidity_sol} SOL AND ${liquidity_usd_temp:.2f} < ${self.min_liquidity_usd}"
                    self._set_analysis_result_with_data(analysis_result, api_data, used_source, reason, extracted_metrics, skip_notifications, token_address)
                    analysis_result['liquidity_usd'] = liquidity_usd_temp
                    logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low liquidity BOTH SOL and USD)")
                    return analysis_result
                elif sol_liquidity_insufficient:
                    logger.info(f"SOL liquidity low ({liquidity_sol_from_api:.2f} < {self.min_liquidity_sol}) but USD liquidity sufficient (${liquidity_usd_temp:.2f} >= ${self.min_liquidity_usd}) - PROCEEDING")
                elif usd_liquidity_insufficient:
                    logger.info(f"USD liquidity low (${liquidity_usd_temp:.2f} < ${self.min_liquidity_usd}) but SOL liquidity sufficient ({liquidity_sol_from_api:.2f} >= {self.min_liquidity_sol}) - PROCEEDING")

            # Early rejection based on liquidity (USD) - using api_data
            liquidity_usd_from_api = api_data.get('liquidity', 0) # DexScreener provides 'liquidity' as USD

            # Hybrid API liquidity removed

            logger.info(f"💰 LIQUIDITY DEBUG for {token_address}: ${liquidity_usd_from_api} (source: {used_source})")

            # Skip liquidity check for GMGN channel tokens
            if not is_gmgn_channel and liquidity_usd_from_api < self.min_liquidity_usd:
                reason = f"Low liquidity: ${liquidity_usd_from_api:.2f} < ${self.min_liquidity_usd}"
                self._set_analysis_result_with_data(analysis_result, api_data, used_source, reason, extracted_metrics, skip_notifications, token_address)
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low liquidity USD)")
                return analysis_result

            # Early rejection based on market cap - using api_data with meme coin verification
            market_cap_from_api = api_data.get('marketCap', 0)

            # Backup calculation for meme coins: market_cap = 1_billion * price
            price_usd = api_data.get('priceUsd', 0)
            if price_usd > 0:
                calculated_market_cap = 1_000_000_000 * float(price_usd)
                if market_cap_from_api == 0 or abs(market_cap_from_api - calculated_market_cap) / calculated_market_cap > 0.5:
                    market_cap_from_api = calculated_market_cap

            if market_cap_from_api < self.min_marketcap_usd:
                # CRITICAL: Set exists=True since we successfully got API data
                analysis_result['exists'] = True
                analysis_result['price_source'] = used_source
                analysis_result['reason'] = f"Low market cap: ${market_cap_from_api:.2f} < ${self.min_marketcap_usd}"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                # Set basic token data before returning - FIXED KEY MAPPING
                analysis_result['price'] = api_data.get('price', api_data.get('priceUsd', 0.0))
                analysis_result['market_cap'] = market_cap_from_api
                analysis_result['volume_24h'] = api_data.get('volume_24h', api_data.get('volume24h', 0))
                analysis_result['liquidity_usd'] = api_data.get('liquidity', 0)
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low market cap)")
                return analysis_result

            # Early rejection based on volume (5m USD) - using api_data
            # Ensure 'volume5m' is correctly populated by get_dex_screener_data or calculated
            vol_5m_from_api = api_data.get('volume5m', 0)

            # Skip volume check for GMGN channel tokens
            if not is_gmgn_channel and vol_5m_from_api < self.min_vol_5m_usd:
                # CRITICAL: Set exists=True since we successfully got API data
                analysis_result['exists'] = True
                analysis_result['price_source'] = used_source
                analysis_result['reason'] = f"Low 5m volume: ${vol_5m_from_api:.2f} < ${self.min_vol_5m_usd}"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                # Set basic token data before returning - FIXED KEY MAPPING
                analysis_result['price'] = api_data.get('price', api_data.get('priceUsd', 0.0))
                analysis_result['market_cap'] = api_data.get('market_cap', api_data.get('marketCap', api_data.get('fdv', 0)))
                analysis_result['volume_24h'] = api_data.get('volume_24h', api_data.get('volume24h', 0))
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low volume)")
                return analysis_result

            # Check holder count if available - using api_data
            holder_count_from_api = api_data.get('holderCount', 0)
            if holder_count_from_api > 0 and holder_count_from_api < self.min_holder_count:
                # CRITICAL: Set exists=True since we successfully got API data
                analysis_result['exists'] = True
                analysis_result['price_source'] = used_source
                analysis_result['reason'] = f"Low holder count: {holder_count_from_api} < {self.min_holder_count}"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                # Set basic token data before returning
                analysis_result['price'] = api_data.get('price', 0.0)
                analysis_result['market_cap'] = api_data.get('marketCap', 0)
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low holder count)")
                return analysis_result

            # Check FDV change if available (for trending tokens) - using api_data
            fdv_change_5m_from_api = api_data.get('fdvChange5m', 0) # Ensure this key is in api_data
            if fdv_change_5m_from_api > 0 and fdv_change_5m_from_api < self.min_fdv_change_5m_pct: # Ensure this is a percentage
                # CRITICAL: Set exists=True since we successfully got API data
                analysis_result['exists'] = True
                analysis_result['price_source'] = used_source
                analysis_result['reason'] = f"Low FDV change: {fdv_change_5m_from_api:.2f}% < {self.min_fdv_change_5m_pct}%"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                # Set basic token data before returning
                analysis_result['price'] = api_data.get('price', 0.0)
                analysis_result['market_cap'] = api_data.get('marketCap', 0)
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (low FDV change)")
                return analysis_result

            # Check bundled percentage if available - using api_data
            bundled_pct_from_api = api_data.get('bundledPct', 0) # Ensure this key is in api_data
            if bundled_pct_from_api > self.max_bundled_pct:
                # CRITICAL: Set exists=True since we successfully got API data
                analysis_result['exists'] = True
                analysis_result['price_source'] = used_source
                analysis_result['reason'] = f"High bundled percentage: {bundled_pct_from_api:.2f}% > {self.max_bundled_pct}%"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                # Set basic token data before returning

            # CRITICAL FIX: Check for high multipliers (3x, 4x, 5x) and reject them
            trading_settings = self.config_manager.get_section('trading_settings')
            prevent_high_multipliers = trading_settings.get('prevent_buy_if_x_multiplier', True) if trading_settings else True
            if prevent_high_multipliers:
                # Get price changes from API data
                price_change_5m = api_data.get('price_change_m5', 0) or 0
                price_change_1h = api_data.get('price_change_h1', 0) or 0
                price_change_6h = api_data.get('price_change_h6', 0) or 0
                price_change_24h = api_data.get('price_change_h24', 0) or 0

                # Convert to absolute values for checking
                max_price_change = max(abs(price_change_5m), abs(price_change_1h), abs(price_change_6h), abs(price_change_24h))

                # REJECT tokens with 3x+ multipliers (300%+ gains)
                if max_price_change >= 200:  # 200% = 3x multiplier
                    logger.warning(f"🚫 REJECTING HIGH MULTIPLIER TOKEN: {token_address}")
                    logger.warning(f"   Max price change: {max_price_change:.1f}% (>= 200% = 3x)")
                    logger.warning(f"   Price changes - 5m: {price_change_5m:.1f}%, 1h: {price_change_1h:.1f}%, 6h: {price_change_6h:.1f}%, 24h: {price_change_24h:.1f}%")

                    # CRITICAL: Set exists=True since we successfully got API data
                    analysis_result['exists'] = True
                    analysis_result['price_source'] = used_source
                    analysis_result['reason'] = f"High multiplier token rejected: {max_price_change:.1f}% gain (>= 3x). Only buying NEW coins with max 2x."
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    # Add price change data for debugging
                    analysis_result['price_change_5m'] = price_change_5m
                    analysis_result['price_change_1h'] = price_change_1h
                    analysis_result['price_change_6h'] = price_change_6h
                    analysis_result['price_change_24h'] = price_change_24h
                    analysis_result['max_price_change'] = max_price_change
                    return analysis_result
                elif max_price_change >= 100:  # 100% = 2x multiplier (warn but allow)
                    logger.info(f"⚠️ 2X MULTIPLIER TOKEN: {token_address} - {max_price_change:.1f}% gain (allowing as it's <= 2x)")
                analysis_result['price'] = api_data.get('price', 0.0)
                analysis_result['market_cap'] = api_data.get('marketCap', 0)
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (high bundled percentage)")
                return analysis_result

            # ========================================
            # MARK TOKEN AS FOUND BEFORE RUG PROTECTION
            # ========================================
            # CRITICAL: Set exists=True since we successfully got API data
            # Even if rug protection rejects the token, it was found on DexScreener
            analysis_result['exists'] = True
            analysis_result['price_source'] = used_source

            # ========================================
            # COMPREHENSIVE RUG PROTECTION CHECKS
            # ========================================
            # CRITICAL: This must run BEFORE any age validation to ensure proper rug detection

            logger.info(f"🛡️ STARTING RUG PROTECTION ANALYSIS for {token_address}")

            trading_settings = self.config_manager.get_section('trading_settings')
            rug_protection_config = trading_settings.get('rug_protection', {}) if trading_settings else {}

            # CRITICAL: Extract pair age BEFORE rug protection checks
            pair_age_mins_from_api = api_data.get('pairAge', 0)  # Ensure 'pairAge' is in api_data

            # COMPREHENSIVE DEBUG: Log rug protection state and all relevant data
            logger.info(f"🛡️ RUG PROTECTION SYSTEM STATUS for {token_address}:")
            logger.info(f"   ✓ Config loaded: {bool(rug_protection_config)}")
            logger.info(f"   ✓ Enabled: {rug_protection_config.get('enabled', True)}")
            logger.info(f"   ✓ Data source: {used_source}")
            logger.info(f"   ✓ Token age: {pair_age_mins_from_api:.2f} minutes")
            logger.info(f"   ✓ Liquidity USD: ${liquidity_usd_from_api:,.2f}")
            logger.info(f"   ✓ Volume 5m USD: ${vol_5m_from_api:,.2f}")
            logger.info(f"   ✓ Market Cap: ${market_cap_from_api:,.2f}")
            logger.info(f"   ✓ API data keys: {list(api_data.keys()) if isinstance(api_data, dict) else 'Not a dict'}")
            logger.info(f"   ✓ Rug protection config: {rug_protection_config}")

            if rug_protection_config.get('enabled', True):
                logger.info(f"🛡️ EXECUTING RUG PROTECTION CHECKS for {token_address}")
                logger.info(f"🛡️ ========================================")

                # RUG CHECK 1: Liquidity Protection (Low liquidity = easy to rug)
                min_rug_liquidity = rug_protection_config.get('min_liquidity_usd', 8000)  # SURGICAL FIX: Updated default
                logger.info(f"🛡️ RUG CHECK 1 - LIQUIDITY PROTECTION:")
                logger.info(f"   Current liquidity: ${liquidity_usd_from_api:,.2f}")
                logger.info(f"   Minimum required: ${min_rug_liquidity:,.2f}")
                logger.info(f"   Status: {'✅ PASS' if liquidity_usd_from_api >= min_rug_liquidity else '❌ FAIL'}")

                if liquidity_usd_from_api < min_rug_liquidity:
                    analysis_result['reason'] = f"🚨 RUG RISK: Low liquidity ${liquidity_usd_from_api:,.0f} < ${min_rug_liquidity:,.0f} (easy to rug)"
                    # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    analysis_result['rug_risk'] = "HIGH_LIQUIDITY_RISK"
                    # Set basic token data before returning
                    analysis_result['price'] = api_data.get('price', 0.0)
                    analysis_result['market_cap'] = market_cap_from_api
                    analysis_result['liquidity_usd'] = liquidity_usd_from_api
                    analysis_result['name'] = api_data.get('name', 'Unknown')
                    analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                    # Add notification fields
                    self._add_notification_fields(analysis_result, token_address, api_data)
                    logger.error(f"🚨 RUG PROTECTION TRIGGERED: Liquidity risk detected for {token_address}")
                    return analysis_result

                # RUG CHECK 2: Volume (5m) > $9.5K (No volume = fake token or bot trap)
                min_rug_volume = rug_protection_config.get('min_volume_5m_usd', 9500)
                logger.info(f"🛡️ RUG CHECK 2 - VOLUME PROTECTION:")
                logger.info(f"   Current volume (5m): ${vol_5m_from_api:,.2f}")
                logger.info(f"   Minimum required: ${min_rug_volume:,.2f}")
                logger.info(f"   Status: {'✅ PASS' if vol_5m_from_api >= min_rug_volume else '❌ FAIL'}")

                if vol_5m_from_api < min_rug_volume:
                    analysis_result['reason'] = f"🚨 RUG RISK: Low volume ${vol_5m_from_api:,.0f} < ${min_rug_volume:,.0f} (suspicious token)"
                    # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    analysis_result['rug_risk'] = "HIGH_VOLUME_RISK"
                    # Set basic token data before returning
                    analysis_result['price'] = api_data.get('price', 0.0)
                    analysis_result['market_cap'] = market_cap_from_api
                    analysis_result['liquidity_usd'] = liquidity_usd_from_api
                    analysis_result['name'] = api_data.get('name', 'Unknown')
                    analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                    # Add notification fields
                    self._add_notification_fields(analysis_result, token_address, api_data)
                    logger.error(f"🚨 RUG PROTECTION TRIGGERED: Volume risk detected for {token_address}")
                    return analysis_result

                # RUG CHECK 3: FDV / Market Cap ≥ $14K (Very low FDV = likely honeypot)
                min_rug_fdv = rug_protection_config.get('min_fdv_usd', 14000)
                fdv_from_api = api_data.get('fdv', market_cap_from_api)  # Use FDV if available, fallback to market cap
                logger.info(f"🛡️ RUG CHECK 3 - FDV PROTECTION:")
                logger.info(f"   Current FDV: ${fdv_from_api:,.2f}")
                logger.info(f"   Minimum required: ${min_rug_fdv:,.2f}")
                logger.info(f"   Status: {'✅ PASS' if fdv_from_api >= min_rug_fdv else '❌ FAIL'}")

                if fdv_from_api < min_rug_fdv:
                    analysis_result['reason'] = f"🚨 RUG RISK: Low FDV ${fdv_from_api:,.0f} < ${min_rug_fdv:,.0f} (likely honeypot)"
                    # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    analysis_result['rug_risk'] = "HIGH_FDV_RISK"
                    # Set basic token data before returning
                    analysis_result['price'] = api_data.get('price', 0.0)
                    analysis_result['market_cap'] = market_cap_from_api
                    analysis_result['liquidity_usd'] = liquidity_usd_from_api
                    analysis_result['name'] = api_data.get('name', 'Unknown')
                    analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                    # Add notification fields
                    self._add_notification_fields(analysis_result, token_address, api_data)
                    logger.error(f"🚨 RUG PROTECTION TRIGGERED: FDV risk detected for {token_address}")
                    return analysis_result

                # RUG CHECK 4: Top 10 holders < 30% (High concentration = whale rug risk)
                max_top10_pct = rug_protection_config.get('max_top10_holders_percent', 30.0)
                top10_pct_from_api = api_data.get('top10HolderPercent', 0)
                logger.info(f"🛡️ RUG CHECK 4 - WHALE CONCENTRATION PROTECTION:")
                logger.info(f"   Top 10 holders: {top10_pct_from_api:.1f}%")
                logger.info(f"   Maximum allowed: {max_top10_pct:.1f}%")
                logger.info(f"   Status: {'✅ PASS' if top10_pct_from_api <= max_top10_pct else '❌ FAIL'}")

                if top10_pct_from_api > max_top10_pct:
                    analysis_result['reason'] = f"🚨 RUG RISK: High whale concentration {top10_pct_from_api:.1f}% > {max_top10_pct:.1f}% (whale rug risk)"
                    # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    analysis_result['rug_risk'] = "HIGH_WHALE_RISK"
                    # Set basic token data before returning
                    analysis_result['price'] = api_data.get('price', 0.0)
                    analysis_result['market_cap'] = market_cap_from_api
                    analysis_result['liquidity_usd'] = liquidity_usd_from_api
                    analysis_result['name'] = api_data.get('name', 'Unknown')
                    analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                    # Add notification fields
                    self._add_notification_fields(analysis_result, token_address, api_data)
                    logger.error(f"🚨 RUG PROTECTION TRIGGERED: Whale concentration risk detected for {token_address}")
                    return analysis_result

                # RUG CHECK 5: Token age check (use same parameter as main age filter)
                min_rug_age = rug_protection_config.get('min_token_age_minutes', self.min_pair_age_mins)
                logger.info(f"🛡️ RUG CHECK 5 - AGE PROTECTION:")
                logger.info(f"   Current age: {pair_age_mins_from_api:.2f} minutes")
                logger.info(f"   Minimum required: {min_rug_age:.2f} minutes")
                logger.info(f"   Status: {'✅ PASS' if pair_age_mins_from_api >= min_rug_age else '❌ FAIL'}")

                if pair_age_mins_from_api < min_rug_age:
                    analysis_result['reason'] = f"🚨 RUG RISK: Too new {pair_age_mins_from_api:.1f}min < {min_rug_age:.1f}min (instant rug window)"
                    # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                    analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                    analysis_result['skip_notifications'] = skip_notifications
                    analysis_result['rug_risk'] = "HIGH_INSTANT_RUG_RISK"
                    # Set basic token data before returning
                    analysis_result['price'] = api_data.get('price', 0.0)
                    analysis_result['market_cap'] = market_cap_from_api
                    analysis_result['liquidity_usd'] = liquidity_usd_from_api
                    analysis_result['name'] = api_data.get('name', 'Unknown')
                    analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                    # Add notification fields
                    self._add_notification_fields(analysis_result, token_address, api_data)
                    logger.error(f"🚨 RUG PROTECTION TRIGGERED: Instant rug risk detected for {token_address}")
                    return analysis_result

                # RUG CHECK 6: Blacklist, Mintable = FALSE (Mintable = infinite mint rug risk)
                if rug_protection_config.get('check_blacklist', True):
                    is_blacklisted = api_data.get('isBlacklisted', False)
                    logger.info(f"🛡️ RUG CHECK 6a - BLACKLIST PROTECTION:")
                    logger.info(f"   Blacklisted: {is_blacklisted}")
                    logger.info(f"   Status: {'❌ FAIL' if is_blacklisted else '✅ PASS'}")

                    if is_blacklisted:
                        analysis_result['reason'] = f"🚨 RUG RISK: Token is blacklisted (known scam/rug)"
                        analysis_result['confidence'] = 0.0
                        analysis_result['skip_notifications'] = skip_notifications
                        analysis_result['rug_risk'] = "BLACKLISTED"
                        # Set basic token data before returning
                        analysis_result['price'] = api_data.get('price', 0.0)
                        analysis_result['market_cap'] = market_cap_from_api
                        analysis_result['liquidity_usd'] = liquidity_usd_from_api
                        analysis_result['name'] = api_data.get('name', 'Unknown')
                        analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                        # Add notification fields
                        self._add_notification_fields(analysis_result, token_address, api_data)
                        logger.error(f"🚨 RUG PROTECTION TRIGGERED: Blacklisted token detected {token_address}")
                        return analysis_result

                # DISABLED: Mintable check disabled per user request
                # if rug_protection_config.get('check_mintable', True):
                #     is_mintable = api_data.get('isMintable', True)  # Default to True (risky) if unknown
                #     logger.info(f"🛡️ RUG CHECK 6b - MINTABLE PROTECTION:")
                #     logger.info(f"   Mintable: {is_mintable}")
                #     logger.info(f"   Status: {'❌ FAIL' if is_mintable else '✅ PASS'}")
                #
                #     if is_mintable:
                #         analysis_result['reason'] = f"🚨 RUG RISK: Token is mintable (infinite mint rug risk)"
                #         # CRITICAL FIX: Calculate normal confidence, don't artificially lower it
                #         analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                #         analysis_result['skip_notifications'] = skip_notifications
                #         analysis_result['rug_risk'] = "HIGH_MINT_RISK"
                #         # Set basic token data before returning
                #         analysis_result['price'] = api_data.get('price', 0.0)
                #         analysis_result['market_cap'] = market_cap_from_api
                #         analysis_result['liquidity_usd'] = liquidity_usd_from_api
                #         analysis_result['name'] = api_data.get('name', 'Unknown')
                #         analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                #         # Add notification fields
                #         self._add_notification_fields(analysis_result, token_address, api_data)
                #         logger.error(f"🚨 RUG PROTECTION TRIGGERED: Mintable token risk detected for {token_address}")
                #         return analysis_result

                logger.info(f"🛡️ RUG CHECK 6b - MINTABLE PROTECTION: DISABLED")
                logger.info(f"   Status: ✅ SKIP (mintable check disabled)")

                # RUG CHECK 7: DEV wallet hasn't sold (First dev sale = instant dump trigger)
                if rug_protection_config.get('check_dev_wallet_activity', True):
                    dev_sold = api_data.get('devSold', False)
                    logger.info(f"🛡️ RUG CHECK 7 - DEV WALLET PROTECTION:")
                    logger.info(f"   Dev sold: {dev_sold}")
                    logger.info(f"   Status: {'⚠️ WARNING' if dev_sold else '✅ PASS'}")

                    if dev_sold:
                        # Apply confidence penalty but don't reject completely
                        dev_penalty = rug_protection_config.get('dev_sell_confidence_penalty', 0.3)
                        logger.warning(f"🚨 RUG PROTECTION WARNING: Dev wallet sold detected for {token_address} - applying {dev_penalty} confidence penalty")
                        # This will be applied in confidence calculation
                        api_data['dev_sell_penalty'] = dev_penalty

                logger.info(f"🛡️ ========================================")
                logger.info(f"✅ RUG PROTECTION: ALL CHECKS PASSED for {token_address}")
                logger.info(f"🛡️ Token is SAFE to proceed with trading analysis")
                logger.info(f"🛡️ ========================================")
            else:
                logger.warning(f"🛡️ RUG PROTECTION: DISABLED for {token_address} - PROCEEDING WITHOUT PROTECTION")
                logger.warning(f"⚠️ WARNING: This token has not been checked for rug risks!")

            # Age check with sliding scale based on liquidity - using api_data
            # Note: pair_age_mins_from_api already defined above for rug protection
            liquidity_usd_from_api = api_data.get('liquidity', 0) # DexScreener provides 'liquidity' as USD

            # Implement sliding scale for age based on liquidity
            # Higher liquidity allows for older tokens
            min_age_required = self.min_pair_age_mins
            max_age_allowed = self.max_pair_age_mins

            # If liquidity is high, adjust the age requirements
            if liquidity_usd_from_api >= 30000:  # Very high liquidity
                min_age_required = 0.5  # Only 30 seconds old needed for high liquidity tokens
                max_age_allowed = float('inf')  # No maximum age for high liquidity tokens
                logger.info(f"High liquidity (${liquidity_usd_from_api:.2f}) detected - reducing minimum age requirement to {min_age_required:.1f}m and removing maximum age limit")
            elif liquidity_usd_from_api >= 15000:  # High liquidity
                min_age_required = 1.0  # Only 1 minute old needed
                max_age_allowed = float('inf')  # No maximum age for high liquidity tokens
                logger.info(f"Good liquidity (${liquidity_usd_from_api:.2f}) detected - reducing minimum age requirement to {min_age_required:.1f}m and removing maximum age limit")

            # Apply the adjusted age checks
            if pair_age_mins_from_api < min_age_required:
                analysis_result['reason'] = f"Too new: {pair_age_mins_from_api} mins < {min_age_required} mins"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (too new)")
                # Set basic token data before returning
                analysis_result['price'] = api_data.get('price', 0.0)
                analysis_result['market_cap'] = market_cap_from_api
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                return analysis_result
            if pair_age_mins_from_api > max_age_allowed and max_age_allowed != float('inf'):
                analysis_result['reason'] = f"Too old: {pair_age_mins_from_api} mins > {max_age_allowed} mins"
                analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
                # Add skip_notifications flag to the result so it can be used by the bot_controller
                analysis_result['skip_notifications'] = skip_notifications
                logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address} (too old)")
                # Set basic token data before returning
                analysis_result['price'] = api_data.get('price', 0.0)
                analysis_result['market_cap'] = market_cap_from_api
                analysis_result['liquidity_usd'] = liquidity_usd_from_api
                analysis_result['name'] = api_data.get('name', 'Unknown')
                analysis_result['symbol'] = api_data.get('symbol', 'Unknown')
                # Add notification fields
                self._add_notification_fields(analysis_result, token_address, api_data)
                return analysis_result

            # CRITICAL FIX: Update analysis_result with proper key mapping for GMGN tokens
            analysis_result['price'] = api_data.get('price', api_data.get('priceUsd', 0.0))
            analysis_result['market_cap'] = market_cap_from_api if market_cap_from_api > 0 else api_data.get('fdv', 0)
            analysis_result['liquidity_usd'] = liquidity_usd_from_api
            analysis_result['liquidity_sol'] = liquidity_sol_from_api
            analysis_result['volume_5m'] = vol_5m_from_api
            analysis_result['volume_1h'] = api_data.get('volume_1h', api_data.get('volume1h', vol_5m_from_api * 12))
            analysis_result['volume_24h'] = api_data.get('volume_24h', api_data.get('volume24h', vol_5m_from_api * 288))
            analysis_result['pair_age_minutes'] = pair_age_mins_from_api
            analysis_result['tx_count_24h'] = api_data.get('txCount', 0) # Ensure key matches DexScreener output
            analysis_result['fdv'] = api_data.get('fdv', 0.0)
            analysis_result['fdv_change_5m'] = fdv_change_5m_from_api
            analysis_result['holder_count'] = holder_count_from_api
            analysis_result['bundled_pct'] = bundled_pct_from_api
            analysis_result['name'] = api_data.get('name', 'Unknown')
            analysis_result['symbol'] = api_data.get('symbol', 'Unknown')

            # CRITICAL: Add fields that notification formatter expects
            # Format token age for display
            if pair_age_mins_from_api > 0:
                hours = int(pair_age_mins_from_api // 60)
                minutes = int(pair_age_mins_from_api % 60)
                if hours > 0:
                    analysis_result['token_age'] = f"{hours}h {minutes}m"
                else:
                    analysis_result['token_age'] = f"{minutes}m"
            else:
                analysis_result['token_age'] = "Unknown"

            # CRITICAL FIX: Add volatility (24h price change) for notifications
            price_change_24h = api_data.get('price_change_24h', 0)
            if price_change_24h != 0:
                analysis_result['volatility'] = f"{price_change_24h:+.1f}%"
            else:
                analysis_result['volatility'] = "0.0%"

            # CRITICAL FIX: Add 5min volume for notifications
            analysis_result['volume_5m'] = vol_5m_from_api

            # CRITICAL FIX: Add 1h transaction count for notifications
            tx_count_1h = api_data.get('txCount1h', 0)
            if tx_count_1h == 0:
                tx_count_24h = api_data.get('txCount', 0)
                if tx_count_24h > 0:
                    # Estimate 1h transactions as 1/24 of 24h transactions
                    tx_count_1h = max(1, int(tx_count_24h / 24))
            analysis_result['tx_count_1h'] = tx_count_1h



            # Add notification fields that the formatter expects
            analysis_result['token_address'] = token_address
            analysis_result['token_name'] = analysis_result['name']

            # CRITICAL FIX: Keep volume_24h as numeric value for bot_controller formatting
            # Don't format as string here - let bot_controller handle the formatting
            # analysis_result['volume_24h'] is already set as numeric value above

            # The 'extracted_metrics' from kwargs (originally from signal_handler)
            # will now be directly used for the final confidence calculation.
            # We no longer call self._extract_metrics_from_message here.
            final_metrics_for_confidence = extracted_metrics if extracted_metrics else {}
            analysis_result['message_metrics'] = final_metrics_for_confidence # Store what was used

            # Calculate final confidence score using API data and metrics from signal_handler
            logger.info(f"Confidence calculation input data for {token_address}: {list(api_data.keys()) if isinstance(api_data, dict) else type(api_data)}")
            analysis_result['confidence'] = self._calculate_confidence(api_data, final_metrics_for_confidence)

            # If confidence is still too low after all checks, set a reason
            if analysis_result['confidence'] < 0.3: # Example threshold for "too low"
                 if not analysis_result['reason']: # Only set if no other rejection reason
                    analysis_result['reason'] = "Confidence score too low based on overall metrics."

            # Update price data for technical analysis features
            self.update_price_data(token_address, analysis_result['price'], analysis_result['volume_5m'])

            # Add skip_notifications flag to the result so it can be used by the bot_controller
            analysis_result['skip_notifications'] = skip_notifications
            logger.info(f"Setting skip_notifications={skip_notifications} in analysis result for {token_address}")

        except Exception as e:
            logger.error(f"Error analyzing {token_address}: {e}", exc_info=True)
            analysis_result['reason'] = f"Analysis error: {str(e)}"

        return analysis_result

    def _add_notification_fields(self, analysis_result: Dict[str, Any], token_address: str, api_data: Dict[str, Any]) -> None:
        """Helper function to add notification fields to analysis result"""
        analysis_result['token_address'] = token_address
        analysis_result['token_name'] = analysis_result.get('name', 'Unknown')

        # Format token age for display
        pair_age_mins = api_data.get('pairAge', 0)
        if pair_age_mins > 0:
            hours = int(pair_age_mins // 60)
            minutes = int(pair_age_mins % 60)
            if hours > 0:
                analysis_result['token_age'] = f"{hours}h {minutes}m"
            else:
                analysis_result['token_age'] = f"{minutes}m"
        else:
            analysis_result['token_age'] = "Unknown"

        # Add 1h transaction count (estimate from 24h if needed)
        tx_count_1h = api_data.get('txCount1h', 0)
        if tx_count_1h == 0:
            tx_count_24h = api_data.get('txCount', 0)
            if tx_count_24h > 0:
                # Estimate 1h transactions as 1/24 of 24h transactions
                tx_count_1h = max(1, int(tx_count_24h / 24))
        analysis_result['tx_count_1h'] = tx_count_1h

        # Add volatility (24h price change)
        price_change_24h = api_data.get('price_change_24h', 0)
        if price_change_24h != 0:
            analysis_result['volatility'] = f"{price_change_24h:+.1f}%"
        else:
            analysis_result['volatility'] = "0.0%"

        # CRITICAL FIX: Keep volume_24h as numeric value for bot_controller formatting
        # Don't format as string here - let bot_controller handle the formatting
        volume_24h = api_data.get('volume_24h', api_data.get('volume24h', 0))
        analysis_result['volume_24h'] = volume_24h  # Keep as numeric value

        # CRITICAL FIX: Add volume_5m for notifications (missing from helper function)
        volume_5m = api_data.get('volume_5m', api_data.get('volume5m', 0))
        analysis_result['volume_5m'] = volume_5m  # Keep as numeric value

    async def get_dex_screener_data(self, token_address: str, force_fresh: bool = False, is_gmgn_channel: bool = False) -> Dict[str, Any]:
        """
        Get token data from DexScreener using the improved implementation from independent_signal_bot.

        Args:
            token_address: The token address to get data for
            force_fresh: Whether to bypass cache and force fresh data (default: False)
            is_gmgn_channel: Whether this is a GMGN channel token (default: False)

        Returns:
            Dict containing token data or error information
        """
        if force_fresh:
            logger.info(f"Fetching fresh DexScreener data for {token_address} (bypassing cache)")
        else:
            logger.info(f"Fetching DexScreener data for {token_address}")

        try:
            # Use the endpoint format from config or default
            dexscreener_endpoint = self.config_manager.get('api_endpoints', 'dexscreener',
                                                          default="https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}")

            # CRITICAL FIX: Validate token address properly for GMGN and pump.fun tokens
            if not token_address or len(token_address) < 20:
                logger.error(f"Invalid token address format: '{token_address}'")
                return {"exists": False, "error": f"Invalid token address format: {token_address}"}

            # PUMP.FUN tokens ending with "pump" are VALID (44 characters)
            if token_address.endswith("pump") and len(token_address) == 44:
                logger.info(f"PUMP.FUN token detected: {token_address} - proceeding with analysis")
            elif is_gmgn_channel and len(token_address) < 32:
                logger.warning(f"GMGN channel sent short address: '{token_address}' (length: {len(token_address)})")
                logger.warning(f"Short GMGN address detected - returning failure")
                return {"exists": False, "error": f"Short GMGN address: {token_address} (length: {len(token_address)})"}

            # Always use the exact token address, especially for tokens ending with "pump" suffix
            api_token_address = token_address.strip()

            # Check if token address ends with "pump" and log it
            if token_address.lower().endswith("pump"):
                logger.warning(f"PUMP TOKEN DETECTED: {token_address}")
                logger.warning(f"Using FULL address with 'pump' suffix: {token_address}")

            # Replace the placeholders in the URL
            url = dexscreener_endpoint.replace("{chainId}", "solana").replace("{tokenAddress}", api_token_address)

            # Log the final URL
            logger.info(f"DexScreener API URL: {url}")

            # Set up headers
            headers = {
                "Accept": "application/json",
                "User-Agent": "TradingBot/1.0"
            }

            # Add API key if available
            api_key = self.config_manager.get('api_keys', 'dexscreener_key', default=None)
            if api_key:
                headers['X-API-KEY'] = api_key

            # CRITICAL FIX: Ultra-fast timeout for meme coin position monitoring
            timeout = aiohttp.ClientTimeout(total=self.api_timeouts.get('dexscreener', 1.0))  # 1.0s max for positions

            # Make the API request with retries
            for retry in range(self.api_retries.get('dexscreener', 3)):
                try:
                    # Always use fresh data - caching disabled
                    logger.info(f"Always using fresh data for {token_address} - caching disabled")

                    # Rate limiting: ensure 1.1 second spacing between DexScreener calls
                    current_time = time.time()
                    time_since_last_call = current_time - self.last_dexscreener_call
                    if time_since_last_call < self.dexscreener_spacing:
                        sleep_time = self.dexscreener_spacing - time_since_last_call
                        await asyncio.sleep(sleep_time)

                    # Make the request
                    start_time = time.time()
                    self.last_dexscreener_call = start_time
                    async with self.session.get(url, headers=headers, timeout=timeout) as resp:
                        response_time = time.time() - start_time
                        logger.info(f"DexScreener response received in {response_time:.3f}s with status {resp.status}")

                        if resp.status == 200:
                            data = await resp.json()

                            # Log the response structure
                            if data:
                                logger.info(f"DexScreener API response received for {token_address}")
                                if isinstance(data, dict):
                                    logger.debug(f"Response is a dictionary with keys: {list(data.keys())}")
                                elif isinstance(data, list):
                                    logger.debug(f"Response is a list with {len(data)} items")
                                else:
                                    logger.debug(f"Response is of type: {type(data)}")
                            else:
                                logger.warning(f"Empty response from DexScreener for {token_address}")
                                continue

                            # Parse the response
                            pairs = []

                            # Handle case where response is a direct array
                            if isinstance(data, list):
                                pairs = data
                                logger.info(f"Found {len(pairs)} pairs in the direct array response")
                            # Handle case where response is an object with 'pairs' property
                            elif isinstance(data, dict) and 'pairs' in data:
                                pairs = data['pairs']
                                logger.info(f"Found {len(pairs)} pairs in the 'pairs' property")

                            if not pairs:
                                logger.warning(f"No pairs found in response for {token_address}")
                                return {"exists": False, "error": "No pairs found in response"}

                            # Find the best pair (highest liquidity)
                            best_pair = None
                            highest_liquidity = 0

                            for pair in pairs:
                                # Debug: Log the chainId to see what we're getting
                                chain_id = pair.get('chainId')
                                logger.info(f"Pair chainId: {chain_id}")

                                # DEBUG: Log the entire pair structure to see what we're getting
                                logger.debug(f"Full pair structure: {json.dumps(pair, indent=2)[:500]}...")

                                # Ensure we're looking at Solana pairs - be more flexible with chainId
                                if chain_id and chain_id.lower() not in ['solana', 'sol']:
                                    logger.info(f"Skipping pair with chainId: {chain_id}")
                                    continue

                                # Get liquidity value - try multiple possible structures
                                liquidity = 0

                                # Try the standard structure first
                                if 'liquidity' in pair and isinstance(pair['liquidity'], dict) and 'usd' in pair['liquidity']:
                                    try:
                                        # Ensure we handle None values properly
                                        liquidity_value = pair['liquidity']['usd']
                                        if liquidity_value is not None:
                                            liquidity = float(liquidity_value)
                                        else:
                                            liquidity = 0
                                        logger.debug(f"Found liquidity in standard structure: ${liquidity}")
                                    except (ValueError, TypeError):
                                        liquidity = 0

                                # Try alternative structure: direct liquidity field
                                elif 'liquidity' in pair and isinstance(pair['liquidity'], (int, float)):
                                    try:
                                        liquidity = float(pair['liquidity'])
                                        logger.debug(f"Found liquidity in direct field: ${liquidity}")
                                    except (ValueError, TypeError):
                                        liquidity = 0

                                # Try liquidityUsd field (sometimes used)
                                elif 'liquidityUsd' in pair:
                                    try:
                                        liquidity_value = pair['liquidityUsd']
                                        if liquidity_value is not None:
                                            liquidity = float(liquidity_value)
                                        logger.debug(f"Found liquidity in liquidityUsd field: ${liquidity}")
                                    except (ValueError, TypeError):
                                        liquidity = 0

                                logger.debug(f"Final liquidity value for this pair: ${liquidity}")

                                # Select the pair with highest liquidity (even if 0)
                                if liquidity >= highest_liquidity:
                                    highest_liquidity = liquidity
                                    best_pair = pair
                                    logger.debug(f"Selected new best pair with liquidity: ${highest_liquidity}")

                            if not best_pair:
                                logger.warning(f"No Solana pairs found for {token_address}")
                                return {"exists": False, "error": "No Solana pairs found"}

                            # Accept pairs even with 0 liquidity - let confidence calculation handle it
                            logger.info(f"Processing pair with liquidity: ${highest_liquidity} (accepting even if 0)")

                            # Extract token data from the best pair
                            logger.info(f"Selected pair with liquidity: ${highest_liquidity}")

                            # Create result data structure
                            token_data = {
                                'exists': True,
                                'source': 'DexScreener'
                            }

                            # Extract base token info
                            base_token = best_pair.get('baseToken', {})
                            token_data['name'] = base_token.get('name', 'Unknown')
                            token_data['symbol'] = base_token.get('symbol', 'Unknown')
                            token_data['token_address'] = base_token.get('address', token_address)

                            # Extract price info
                            try:
                                # Safely handle None values
                                price_usd = best_pair.get('priceUsd')
                                price_native = best_pair.get('priceNative')

                                token_data['price'] = float(price_usd) if price_usd is not None else 0
                                token_data['price_native'] = float(price_native) if price_native is not None else 0
                            except (ValueError, TypeError):
                                token_data['price'] = 0
                                token_data['price_native'] = 0

                            # Extract price changes
                            price_changes = best_pair.get('priceChange', {})

                            # Safely handle None values for price changes
                            m5_change = price_changes.get('m5')
                            h1_change = price_changes.get('h1')
                            h6_change = price_changes.get('h6')
                            h24_change = price_changes.get('h24')

                            token_data['price_change_5m'] = float(m5_change) if m5_change is not None else 0
                            token_data['price_change_1h'] = float(h1_change) if h1_change is not None else 0
                            token_data['price_change_6h'] = float(h6_change) if h6_change is not None else 0
                            token_data['price_change_24h'] = float(h24_change) if h24_change is not None else 0

                            # Extract liquidity info
                            if 'liquidity' in best_pair and isinstance(best_pair['liquidity'], dict):
                                # Safely handle None values for liquidity
                                liq_usd = best_pair['liquidity'].get('usd')
                                liq_native = best_pair['liquidity'].get('native')

                                token_data['liquidity'] = float(liq_usd) if liq_usd is not None else 0
                                token_data['liquidity_native'] = float(liq_native) if liq_native is not None else 0
                            else:
                                token_data['liquidity'] = 0
                                token_data['liquidity_native'] = 0

                            # Calculate SOL liquidity using price if needed
                            if self.sol_usd_price > 0 and not token_data.get('liquidity_sol'):
                                token_data['liquidity_sol'] = token_data['liquidity'] / self.sol_usd_price

                            # Extract volume
                            volume_data = best_pair.get('volume', {})

                            # Safely handle None values for volume
                            vol_5m = volume_data.get('m5')
                            vol_1h = volume_data.get('h1')
                            vol_6h = volume_data.get('h6')
                            vol_24h = volume_data.get('h24')

                            token_data['volume5m'] = float(vol_5m) if vol_5m is not None else 0
                            token_data['volume1h'] = float(vol_1h) if vol_1h is not None else 0
                            token_data['volume6h'] = float(vol_6h) if vol_6h is not None else 0
                            token_data['volume24h'] = float(vol_24h) if vol_24h is not None else 0

                            # Extract transaction counts
                            txns = best_pair.get('txns', {})
                            token_data['txCount'] = 0  # Initialize

                            # Try to extract transaction counts for different time periods
                            for period in ['h1', 'h24']:
                                if period in txns and isinstance(txns[period], dict):
                                    # Safely handle None values for transaction counts
                                    buys_val = txns[period].get('buys')
                                    sells_val = txns[period].get('sells')

                                    buys = int(buys_val) if buys_val is not None else 0
                                    sells = int(sells_val) if sells_val is not None else 0
                                    total = buys + sells

                                    if period == 'h24':
                                        token_data['txCount'] = total
                                        token_data['buys24h'] = buys
                                        token_data['sells24h'] = sells
                                    elif period == 'h1':
                                        token_data['txCount1h'] = total
                                        token_data['buys1h'] = buys
                                        token_data['sells1h'] = sells

                            # Extract FDV and market cap
                            try:
                                # Safely handle None values for FDV and market cap
                                fdv_val = best_pair.get('fdv')
                                mcap_val = best_pair.get('marketCap')

                                token_data['fdv'] = float(fdv_val) if fdv_val is not None else 0
                                token_data['marketCap'] = float(mcap_val) if mcap_val is not None else 0
                            except (ValueError, TypeError):
                                token_data['fdv'] = 0
                                token_data['marketCap'] = 0

                            # Extract pair creation time
                            created_at = best_pair.get('pairCreatedAt')
                            if created_at:
                                if isinstance(created_at, str):
                                    token_data['pairAge'] = self._calculate_pair_age(created_at)
                                elif isinstance(created_at, int):
                                    token_data['pairAge'] = self._calculate_pair_age_from_timestamp(created_at)
                            else:
                                token_data['pairAge'] = 0

                            # Extract exchange URL
                            token_data['url'] = best_pair.get('url', '')

                            # Log successful data extraction
                            logger.info(f"Successfully extracted DexScreener data for {token_address}")
                            logger.info(f"Price: ${token_data['price']}, Liquidity: ${token_data['liquidity']}, Volume 24h: ${token_data['volume24h']}")

                            return token_data

                        else:
                            # Non-200 status code
                            logger.warning(f"DexScreener API returned status {resp.status} for {token_address}")
                            error_text = await resp.text()
                            logger.warning(f"Error response: {error_text[:200]}")

                            # Moralis fallback removed

                            # If error is permanent (not 429 rate limit), no need to retry
                            if resp.status not in [429, 502, 503, 504]:
                                return {"exists": False, "error": f"API error (Status {resp.status}): {error_text[:100]}..."}

                except asyncio.TimeoutError:
                    logger.warning(f"Timeout fetching DexScreener data for {token_address} (retry {retry+1}/{self.api_retries.get('dexscreener', 3)})")

                except Exception as e:
                    logger.error(f"Error fetching DexScreener data for {token_address}: {e}")

                # Wait before retrying
                if retry < self.api_retries.get('dexscreener', 3) - 1:
                    await asyncio.sleep(self.api_retry_delays.get('dexscreener', 0.5) * (retry + 1))

            # If we get here, all retries failed
            return {"exists": False, "error": "Failed to fetch data from DexScreener after multiple retries"}

        except Exception as e:
            logger.error(f"Unexpected error fetching DexScreener data: {e}", exc_info=True)
            return {"exists": False, "error": f"Unexpected error: {str(e)}"}

    # Moralis conversion method removed

    async def get_dex_screener_pair_data(self, pair_id: str) -> Dict[str, Any]:
        """
        Fetch pair-specific data from DexScreener using direct API call with retries

        Args:
            pair_id: The pair ID to get data for
            force_fresh: Whether to bypass cache and force fresh data (default: False)
        """
        # Ensure we have a valid session
        if not self.session or self.session.closed:
            try:
                await self.init_sessions()
            except Exception as e:
                logger.error(f"Failed to initialize session for DexScreener: {e}")
                return {"exists": False, "error": "Session initialization failed"}

        # Use direct API call instead of rate limiter
        # Use the correct endpoint format
        dexscreener_endpoint = "https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}"

        # Replace the placeholders in the URL - ensure correct order
        url = dexscreener_endpoint.replace("{chainId}", "solana").replace("{tokenAddress}", pair_id)
        logger.info(f"Using DexScreener pairs endpoint: {url}")

        # Always use fresh data - caching disabled
        logger.debug(f"Always using fresh data for pair {pair_id} - caching disabled")

        # Get API key if available
        api_key = self.config_manager.get('api_keys', 'dexscreener_key', default=None)
        headers = {}
        if api_key:
            headers['X-API-KEY'] = api_key

        # Make the request with retries
        for retry in range(self.api_retries.get('dexscreener', 3)):
            try:
                # CRITICAL FIX: Ultra-fast timeout for meme coin position monitoring
                # Use 1.0s max timeout for fast trading and position monitoring
                timeout = aiohttp.ClientTimeout(total=self.api_timeouts.get('dexscreener', 1.0))

                async with self.session.get(url, headers=headers, timeout=timeout) as resp:
                    if resp.status == 200:
                        try:
                            # Read response text first
                            response_text = await resp.text()

                            # Try to parse as JSON
                            try:
                                data = json.loads(response_text)
                            except json.JSONDecodeError as e:
                                logger.error(f"DexScreener returned invalid JSON for pair {pair_id}: {e}")
                                logger.debug(f"Response text: {response_text[:500]}...")
                                return {"exists": False, "error": "Invalid JSON response"}

                            # Validate the response
                            if not data:
                                logger.error(f"DexScreener returned empty data for pair {pair_id}")
                                return {"exists": False, "error": "Empty response"}

                            # Handle both response formats: direct array or object with 'pairs' property
                            if isinstance(data, list):
                                pairs = data
                                logger.debug(f"DexScreener returned direct array of pairs for {pair_id}")
                            else:
                                if 'pairs' not in data:
                                    logger.error(f"DexScreener response missing 'pairs' key for pair {pair_id}")
                                    return {"exists": False, "error": "No pairs key in response"}
                                pairs = data['pairs']

                            if not pairs:
                                logger.error(f"DexScreener returned empty pairs array for pair {pair_id}")
                                return {"exists": False, "error": "No pairs found"}

                            # Get the first pair (should be only one since we're querying by pair ID)
                            pair = pairs[0]

                            # Log basic pair info
                            logger.info(f"Found pair data for {pair_id}: {pair.get('baseToken', {}).get('symbol', 'Unknown')}/{pair.get('quoteToken', {}).get('symbol', 'Unknown')}")

                            # Extract data with safe conversion
                            try:
                                # Safely handle None values
                                price_usd = pair.get('priceUsd')
                                price = float(price_usd) if price_usd is not None else 0

                                liq_usd = pair.get('liquidity', {}).get('usd')
                                liquidity = float(liq_usd) if liq_usd is not None else 0

                                vol_24h = pair.get('volume', {}).get('h24')
                                volume_24h = float(vol_24h) if vol_24h is not None else 0

                                vol_1h = pair.get('volume', {}).get('h1')
                                volume_1h = float(vol_1h) if vol_1h is not None else 0

                                # CRITICAL FIX: Extract actual 5m volume from API instead of estimating
                                vol_5m = pair.get('volume', {}).get('m5')
                                volume_5m = float(vol_5m) if vol_5m is not None else 0

                                pair_age = self._calculate_pair_age(pair.get('pairCreatedAt', ''))

                                tx_count_val = pair.get('txns', {}).get('h24')
                                tx_count = int(tx_count_val) if tx_count_val is not None else 0

                                fdv_val = pair.get('fdv')
                                fdv = float(fdv_val) if fdv_val is not None else 0

                                price_change_val = pair.get('priceChange', {}).get('m5')
                                price_change_5m = float(price_change_val) if price_change_val is not None else 0

                                # Create result dictionary
                                result = {
                                    "exists": True,
                                    "price": price,
                                    "liquidity": liquidity,
                                    "volume5m": volume_5m,
                                    "volume1h": volume_1h,
                                    "volume24h": volume_24h,
                                    "pairAge": pair_age,
                                    "txCount": tx_count,
                                    "fdv": fdv,
                                    "marketCap": fdv,  # Use FDV as market cap if not provided
                                    "priceChange5m": price_change_5m,
                                    "baseToken": pair.get('baseToken', {}).get('address', ''),
                                    "quoteToken": pair.get('quoteToken', {}).get('address', ''),
                                    "pairAddress": pair.get('pairAddress', ''),
                                    "name": pair.get('baseToken', {}).get('name', 'Unknown'),
                                    "symbol": pair.get('baseToken', {}).get('symbol', 'Unknown')
                                }

                                # Cache the result if we have a rate limiter
                                if hasattr(self, 'rate_limiter'):
                                    cache_key = f"dexscreener_pair_{pair_id}"
                                    self.rate_limiter.cache[cache_key] = {
                                        'data': result,
                                        'timestamp': datetime.now(),
                                        'provider': 'dexscreener'
                                    }

                                return result
                            except (ValueError, TypeError) as e:
                                logger.error(f"Error parsing DexScreener pair data for {pair_id}: {e}")
                                return {"exists": False, "error": f"Data parsing error: {str(e)}"}
                        except aiohttp.ContentTypeError:
                            logger.error(f"DexScreener returned non-JSON response for pair {pair_id}")
                            # Try to read the response content for debugging
                            try:
                                content = await resp.text()
                                logger.debug(f"DexScreener non-JSON response: {content[:500]}...")
                            except:
                                pass
                            return {"exists": False, "error": "Invalid JSON response"}
                    elif resp.status == 429:
                        logger.warning(f"DexScreener rate limit hit for pair {pair_id}, retry {retry+1}/{self.api_retries.get('dexscreener', 3)}")
                        if retry < self.api_retries.get('dexscreener', 3) - 1:
                            # Use absolute minimal backoff for meme coins
                            backoff = self.api_retry_delays.get('dexscreener', 0.1) * (1.2 ** retry) * (0.9 + 0.2 * random.random())
                            logger.debug(f"Backing off for {backoff:.2f}s before retry {retry+1}")
                            await asyncio.sleep(backoff)
                            continue
                        else:
                            return {"exists": False, "error": "Rate limit exceeded, max retries reached"}
                    elif resp.status == 404:
                        logger.warning(f"DexScreener returned 404 for pair {pair_id} - pair may not exist")
                        return {"exists": False, "error": "Pair not found (404)"}
                    elif resp.status >= 500:
                        logger.warning(f"DexScreener server error ({resp.status}) for pair {pair_id}, retry {retry+1}/{self.api_retries.get('dexscreener', 3)}")
                        if retry < self.api_retries.get('dexscreener', 3) - 1:
                            # Use absolute minimal backoff for meme coins
                            backoff = self.api_retry_delays.get('dexscreener', 0.1) * (1.2 ** retry) * (0.9 + 0.2 * random.random())
                            await asyncio.sleep(backoff)
                            continue
                        else:
                            return {"exists": False, "error": f"Server error: HTTP {resp.status}"}
                    else:
                        logger.warning(f"DexScreener returned HTTP {resp.status} for pair {pair_id}")
                        # Try to read the response content for debugging
                        try:
                            content = await resp.text()
                            logger.debug(f"DexScreener error response: {content[:500]}...")
                        except:
                            pass
                        return {"exists": False, "error": f"HTTP {resp.status}"}

            except asyncio.TimeoutError:
                logger.warning(f"DexScreener API timeout for pair {pair_id}")
                if retry < self.api_retries.get('dexscreener', 3) - 1:
                    # Use minimal delay for timeouts to try again immediately
                    await asyncio.sleep(self.api_retry_delays.get('dexscreener', 0.1))
                    continue
                return {"exists": False, "error": "Request timed out"}
            except aiohttp.ClientError as e:
                logger.error(f"DexScreener client error for pair {pair_id}: {e}")
                if retry < self.api_retries.get('dexscreener', 3) - 1:
                    # Use minimal delay for client errors
                    await asyncio.sleep(self.api_retry_delays.get('dexscreener', 0.1))
                    continue
                return {"exists": False, "error": f"Client error: {str(e)}"}
            except Exception as e:
                logger.error(f"Unexpected error fetching DexScreener pair data for pair {pair_id}: {e}", exc_info=True)
                if retry < self.api_retries.get('dexscreener', 3) - 1:
                    # Use minimal delay for general errors
                    await asyncio.sleep(self.api_retry_delays.get('dexscreener', 0.1))
                    continue
                return {"exists": False, "error": str(e)}

        return {"exists": False, "error": "Max retries reached"}

    def _set_analysis_result_with_data(self, analysis_result: dict, api_data: dict, used_source: str, reason: str, extracted_metrics: dict, skip_notifications: bool = False, token_address: str = ""):
        """Set analysis result with common data fields - eliminates duplicate code"""
        analysis_result['exists'] = True
        analysis_result['price_source'] = used_source
        analysis_result['reason'] = reason
        analysis_result['confidence'] = self._calculate_confidence(api_data, extracted_metrics if extracted_metrics else {})
        analysis_result['skip_notifications'] = skip_notifications

        # Set basic token data with proper key mapping
        analysis_result['price'] = api_data.get('price', api_data.get('priceUsd', 0.0))
        analysis_result['market_cap'] = api_data.get('market_cap', api_data.get('marketCap', api_data.get('fdv', 0)))
        analysis_result['volume_24h'] = api_data.get('volume_24h', api_data.get('volume24h', 0))
        analysis_result['name'] = api_data.get('name', 'Unknown')
        analysis_result['symbol'] = api_data.get('symbol', 'Unknown')

        # Add notification fields
        if token_address:
            self._add_notification_fields(analysis_result, token_address, api_data)

    def _check_scam_patterns(self, token_name: str, factors: dict) -> bool:
        """Check for scam patterns in token name - eliminates duplicate code"""
        if re.search(r'^\d+X$', token_name) or re.search(r'^\d+%$', token_name):
            logger.warning(f"Token name '{token_name}' matches a potential scam pattern (e.g., '5X', '100X', '20%')")
            factors["token_name_warning"] = -0.4
            return True
        return False

    # Birdeye API integration removed

    def _calculate_pair_age(self, created_at_str: str) -> int:
        """Calculate pair age in minutes"""
        if not created_at_str:
            return 0

        try:
            created_time = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            age_minutes = (now - created_time).total_seconds() / 60
            return int(age_minutes)
        except Exception:
            return 0

    def _calculate_pair_age_from_timestamp(self, timestamp: int) -> int:
        """Calculate pair age in minutes from Unix timestamp"""
        if not timestamp:
            return 0

        try:
            # Handle different timestamp formats
            if isinstance(timestamp, str):
                timestamp = int(float(timestamp))

            # Check if timestamp is in milliseconds (common for DexScreener)
            if timestamp > 1e12:  # If timestamp is > year 2001 in milliseconds
                timestamp = timestamp / 1000

            # Validate timestamp range (between 2020 and 2030)
            if timestamp < 1577836800 or timestamp > 1893456000:  # 2020-01-01 to 2030-01-01
                logger.warning(f"Invalid timestamp range: {timestamp}")
                return 0

            # Convert timestamp to datetime
            created_dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)

            # Calculate age in minutes
            now = datetime.now(tz=timezone.utc)
            age_minutes = int((now - created_dt).total_seconds() / 60)
            return max(0, age_minutes)
        except (ValueError, TypeError, OSError) as e:
            logger.warning(f"Error calculating pair age from timestamp {timestamp}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error calculating pair age from timestamp {timestamp}: {e}")
            return 0

    def _calculate_confidence(self, data: Dict[str, Any], metrics: Dict[str, str] = None) -> float:
        """
        Calculate confidence score based on token metrics.

        Args:
            data: Token data from DexScreener API
            metrics: Optional metrics extracted from the message text

        Returns:
            Confidence score between 0.0 and 1.0
        """
        # Initialize factors dictionary for detailed logging
        factors = {}

        # Debug logging to see what data we're receiving
        logger.info(f"Confidence calculation input data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        logger.info(f"Key data values: liquidity={data.get('liquidity', 'N/A')}, volume5m={data.get('volume5m', 'N/A')}, pairAge={data.get('pairAge', 'N/A')}")

        # Check for tokens with suspicious names (like "5X", "100X", etc.)
        token_name = data.get('symbol', '').upper()
        self._check_scam_patterns(token_name, factors)

        # RUG PROTECTION: Apply dev sell penalty if detected
        dev_sell_penalty = data.get('dev_sell_penalty', 0)
        if dev_sell_penalty > 0:
            confidence -= dev_sell_penalty
            factors["dev_sell_penalty"] = -dev_sell_penalty
            logger.warning(f"Applied dev sell penalty: -{dev_sell_penalty} to confidence for token")

        # Also check in metrics if available
        elif metrics and metrics.get('token_name'):
            token_name_from_metrics = metrics.get('token_name', '').upper()
            self._check_scam_patterns(token_name_from_metrics, factors)

        # Check for suspicious phrases in the message text that indicate potential scams
        if metrics and metrics.get('message_text'):
            message_text = metrics.get('message_text', '').upper()
            # Check for phrases like "MADE 5X", "100X", "20%", etc.
            if re.search(r'MADE\s+\d+X', message_text) or re.search(r'JUST\s+MADE\s+\d+X', message_text) or \
               re.search(r'\b\d+X\b', message_text) or re.search(r'\b\d+%\b', message_text):
                logger.warning(f"Message contains suspicious phrases like 'MADE 5X', '100X', etc. that indicate potential scams")
                factors["message_content_warning"] = -0.3

        confidence = 0.5  # Base confidence
        factors["base"] = 0.5

        # Apply token name warning if present
        if "token_name_warning" in factors:
            confidence += factors["token_name_warning"]  # This will be negative

        # Apply message content warning if present
        if "message_content_warning" in factors:
            confidence += factors["message_content_warning"]  # This will be negative

        # Liquidity check - updated with new thresholds
        liquidity = data.get('liquidity', 0)
        if liquidity >= 15000:  # Increased threshold for high confidence based on requirements
            confidence += 0.3
            factors["liquidity"] = 0.3
        elif liquidity >= 8000:  # New threshold based on requirements
            confidence += 0.2
            factors["liquidity"] = 0.2
        elif liquidity >= 5000:
            confidence += 0.1
            factors["liquidity"] = 0.1
        elif liquidity < 1000:
            confidence -= 0.2
            factors["liquidity"] = -0.2
        else:
            factors["liquidity"] = 0.0

        # Volume check - updated with new thresholds
        volume = data.get('volume5m', 0)
        if volume >= 9500:  # Updated threshold to match rug protection
            confidence += 0.3
            factors["volume"] = 0.3
        elif volume >= 5000:
            confidence += 0.2
            factors["volume"] = 0.2
        elif volume >= 1000:
            confidence += 0.1
            factors["volume"] = 0.1
        elif volume < 500:
            confidence -= 0.2
            factors["volume"] = -0.2
        else:
            factors["volume"] = 0.0

        # Transaction count check
        tx_count = data.get('txCount', 0)
        if tx_count >= 100:
            confidence += 0.1
            factors["tx_count"] = 0.1
        elif tx_count < 10:
            confidence -= 0.1
            factors["tx_count"] = -0.1
        else:
            factors["tx_count"] = 0.0

        # Age check with liquidity consideration
        age = data.get('pairAge', 0)
        liquidity = data.get('liquidity', 0)

        # For tokens with high liquidity, age is less important
        if liquidity >= 30000:  # Very high liquidity
            if age >= 0.5:  # At least 30 seconds old
                confidence += 0.2
                factors["age"] = 0.2
                logger.info(f"High liquidity token (${liquidity:.2f}) with sufficient age ({age:.2f}m) - applying confidence boost")
            else:
                # Still too new even for high liquidity
                confidence -= 0.1  # Reduced penalty
                factors["age"] = -0.1
            # No penalty for old age with high liquidity
        elif liquidity >= 15000:  # High liquidity
            if 0.5 <= age <= 300:  # Between 30 seconds and 5 hours
                confidence += 0.2
                factors["age"] = 0.2
                logger.info(f"Good liquidity token (${liquidity:.2f}) with ideal age ({age:.2f}m) - applying confidence boost")
            elif age < 0.5:
                confidence -= 0.1  # Reduced penalty for new tokens with good liquidity
                factors["age"] = -0.1
            elif age > 300:
                confidence -= 0.1  # Reduced penalty for old tokens with good liquidity
                factors["age"] = -0.1
        else:
            # Standard age check for normal liquidity
            if 5 <= age <= 60:  # 5 minutes to 1 hour
                confidence += 0.2
                factors["age"] = 0.2
            elif age < 2:
                confidence -= 0.2
                factors["age"] = -0.2
            elif age > 300:  # Penalize tokens older than 5 hours (300 minutes)
                confidence -= 0.3
                factors["age"] = -0.3
            else:
                factors["age"] = 0.0

        # FDV check
        fdv = data.get('fdv', 0)
        if 100000 <= fdv <= 1000000:
            confidence += 0.1
            factors["fdv"] = 0.1
        elif fdv > 10000000:
            confidence -= 0.1
            factors["fdv"] = -0.1
        else:
            factors["fdv"] = 0.0

        # Price change check
        price_change = data.get('price_change_5m', 0)
        if price_change > 50:  # Over 50% gain in 5 minutes
            confidence += 0.1
            factors["price_change"] = 0.1
        elif price_change < -20:  # Over 20% loss in 5 minutes
            confidence -= 0.2
            factors["price_change"] = -0.2
        else:
            factors["price_change"] = 0.0

        # Buy/Sell ratio check
        buys = data.get('txns', {}).get('h1', {}).get('buys', 0)
        sells = data.get('txns', {}).get('h1', {}).get('sells', 0)
        if buys > 0 and sells > 0:
            ratio = buys / sells
            if ratio > 2:  # More than 2x buys than sells
                confidence += 0.1
                factors["buy_sell_ratio"] = 0.1
            elif ratio < 0.5:  # More than 2x sells than buys
                confidence -= 0.1
                factors["buy_sell_ratio"] = -0.1
            else:
                factors["buy_sell_ratio"] = 0.0
        else:
            factors["buy_sell_ratio"] = 0.0

        # If we have metrics from the message, incorporate them
        if metrics:
            # Check if the message mentions a high volume
            if metrics.get('volume'):
                try:
                    # Convert K/M to actual numbers
                    vol_str = metrics['volume']
                    vol_value = self._parse_numeric_value(vol_str)

                    # Adjust confidence based on volume
                    if vol_value >= 10000:
                        confidence += 0.2
                        factors["message_volume"] = 0.2
                    elif vol_value >= 5000:
                        confidence += 0.1
                        factors["message_volume"] = 0.1
                    elif vol_value < 1000:
                        confidence -= 0.1
                        factors["message_volume"] = -0.1
                    else:
                        factors["message_volume"] = 0.0

                    # Override API volume if message volume is higher
                    if vol_value > volume:
                        # Recalculate volume confidence
                        if vol_value >= 10000:
                            confidence += 0.1  # Additional boost for high volume
                            factors["message_volume_boost"] = 0.1
                except (ValueError, TypeError):
                    factors["message_volume"] = 0.0

            # Check if the message mentions high liquidity
            if metrics.get('liquidity_usd'):
                try:
                    liq_str = metrics['liquidity_usd']
                    liq_value = self._parse_numeric_value(liq_str)

                    # Adjust confidence based on liquidity
                    if liq_value >= 20000:
                        confidence += 0.2
                        factors["message_liquidity"] = 0.2
                    elif liq_value >= 10000:
                        confidence += 0.1
                        factors["message_liquidity"] = 0.1
                    elif liq_value < 2000:
                        confidence -= 0.1
                        factors["message_liquidity"] = -0.1
                    else:
                        factors["message_liquidity"] = 0.0

                    # Override API liquidity if message liquidity is higher
                    if liq_value > liquidity:
                        # Recalculate liquidity confidence
                        if liq_value >= 20000:
                            confidence += 0.1  # Additional boost for high liquidity
                            factors["message_liquidity_boost"] = 0.1
                except (ValueError, TypeError):
                    factors["message_liquidity"] = 0.0

            # Check if the message mentions market cap - updated with new thresholds
            if metrics.get('mcp'):
                try:
                    mcp_str = metrics['mcp']
                    mcp_value = self._parse_numeric_value(mcp_str)

                    # Adjust confidence based on market cap with new threshold from requirements
                    if mcp_value >= 100000:
                        confidence += 0.2
                        factors["message_mcp"] = 0.2
                    elif mcp_value >= 14000:  # New threshold based on requirements
                        confidence += 0.1
                        factors["message_mcp"] = 0.1
                    elif mcp_value < 5000:
                        confidence -= 0.1
                        factors["message_mcp"] = -0.1
                    else:
                        factors["message_mcp"] = 0.0
                except (ValueError, TypeError):
                    factors["message_mcp"] = 0.0

            # Check if the message mentions holders
            if metrics.get('holders'):
                try:
                    holders_str = metrics['holders']
                    holders = int(holders_str)

                    # Adjust confidence based on holder count
                    if holders >= 500:
                        confidence += 0.2
                        factors["message_holders"] = 0.2
                    elif holders >= 100:
                        confidence += 0.1
                        factors["message_holders"] = 0.1
                    elif holders < 20:
                        confidence -= 0.1
                        factors["message_holders"] = -0.1
                    else:
                        factors["message_holders"] = 0.0
                except (ValueError, TypeError):
                    factors["message_holders"] = 0.0

            # Check if the message mentions transactions
            if metrics.get('txs'):
                try:
                    txs_str = metrics['txs']
                    txs = int(txs_str)

                    # Adjust confidence based on transaction count
                    if txs >= 300:
                        confidence += 0.2
                        factors["message_txs"] = 0.2
                    elif txs >= 100:
                        confidence += 0.1
                        factors["message_txs"] = 0.1
                    elif txs < 20:
                        confidence -= 0.1
                        factors["message_txs"] = -0.1
                    else:
                        factors["message_txs"] = 0.0
                except (ValueError, TypeError):
                    factors["message_txs"] = 0.0

        # Log all factors for debugging
        logger.debug(f"Confidence calculation factors: {factors}")

        # Clamp confidence to [0.0, 1.0]
        final_confidence = max(0.0, min(1.0, confidence))

        return final_confidence

    def _extract_metrics_from_message(self, message_text: str) -> Dict[str, str]:
        """
        Extract metrics from a signal message text.

        Args:
            message_text: The text of the signal message

        Returns:
            Dictionary of extracted metrics
        """
        if not message_text:
            return {}

        # Import regex patterns from signal_handler
        try:
            from signal_handler import (
                TOKEN_NAME_REGEX, VOLUME_REGEX, MCP_REGEX, FDV_REGEX,
                FDV_PERCENT_REGEX, LIQUIDITY_REGEX, LIQUIDITY_USD_REGEX,
                HOLDERS_REGEX, OPEN_REGEX, STATUS_REGEX, TXS_REGEX,
                VOLUME_SPECIFIC_REGEX, MCP_SPECIFIC_REGEX, LIQ_SPECIFIC_REGEX,
                TXS_VOL_REGEX, VOLUME_5M_REGEX, HOLDER_SPECIFIC_REGEX,
                OPEN_SPECIFIC_REGEX, MARKDOWN_TOKEN_NAME_REGEX
            )
        except ImportError:
            # Define fallback regex patterns if import fails
            TOKEN_NAME_REGEX = re.compile(r"\$([A-Za-z0-9X_-]+)\s*\(([^)]+)\)", re.IGNORECASE)
            MARKDOWN_TOKEN_NAME_REGEX = re.compile(r'\[\*\*([A-Za-z0-9X_\- ]+)\*\*\]', re.IGNORECASE)
            VOLUME_REGEX = re.compile(r"(?:Vol|TXs/Vol|TXs\/Vol).*?\$?\s*([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            MCP_REGEX = re.compile(r"(?:MCP|MC|Market Cap|MCAP).*?\$?\s*([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            FDV_REGEX = re.compile(r"FDV.*?(?:\+|\-)?\$?\s*([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            FDV_PERCENT_REGEX = re.compile(r"FDV.*?(?:\+|\-)?([\d.,]+)%", re.IGNORECASE)
            LIQUIDITY_REGEX = re.compile(r"(?:Liq|Liquidity).*?([\d.,]+)\s*(?:SOL)", re.IGNORECASE)
            LIQUIDITY_USD_REGEX = re.compile(r"(?:Liq|Liquidity).*?\$?\s*([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            HOLDERS_REGEX = re.compile(r"(?:Hodls|Holder|Holders).*?(\d+)", re.IGNORECASE)
            OPEN_REGEX = re.compile(r"(?:Open|Age).*?(\d+)\s*(?:min|m|s|h|ago)", re.IGNORECASE)
            STATUS_REGEX = re.compile(r"Status.*?([\d.,]+)%", re.IGNORECASE)
            TXS_REGEX = re.compile(r"(\d+)\s*(?:txs|TXs)", re.IGNORECASE)
            VOLUME_SPECIFIC_REGEX = re.compile(r"Vol.*?__\w+__:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            MCP_SPECIFIC_REGEX = re.compile(r"MC:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            LIQ_SPECIFIC_REGEX = re.compile(r"Liq:\s*\$?([\d.,]+)(?:K|M|B)?", re.IGNORECASE)
            TXS_VOL_REGEX = re.compile(r"TXs/Vol:\s*\*\*(\d+)\*\*/\*\*\$?([\d.,]+)(?:K|M|B)?\*\*", re.IGNORECASE)
            VOLUME_5M_REGEX = re.compile(r"5m\s*TXs/Vol.*?(\d+)/\$?([\d.,]+)K", re.IGNORECASE)
            HOLDER_SPECIFIC_REGEX = re.compile(r"Holder:\s*(\d+)", re.IGNORECASE)
            OPEN_SPECIFIC_REGEX = re.compile(r"Open:\s*(\d+)s", re.IGNORECASE)

        # Initialize metrics dictionary with message text
        metrics = {'message_text': message_text}

        # Extract token name
        token_name_match = TOKEN_NAME_REGEX.search(message_text)
        if token_name_match:
            metrics['token_name'] = token_name_match.group(1)
            metrics['token_full_name'] = token_name_match.group(2)

        # Try to extract token name from "Token Symbol: $LFG" format
        if not metrics.get('token_name'):
            token_symbol_match = re.search(r'Token Symbol:\s*\$([A-Za-z0-9X]+)', message_text, re.IGNORECASE)
            if token_symbol_match:
                metrics['token_name'] = token_symbol_match.group(1)
                # Try to find a full name nearby
                full_name_match = re.search(r'Token Name:\s*([^\n]+)', message_text, re.IGNORECASE)
                if full_name_match:
                    metrics['token_full_name'] = full_name_match.group(1).strip()

        # Try to extract token name from markdown links [**TokenName**]
        if not metrics.get('token_name') or not metrics.get('token_full_name'):
            markdown_matches = MARKDOWN_TOKEN_NAME_REGEX.finditer(message_text)
            for match in markdown_matches:
                token_name = match.group(1).strip()
                if token_name and len(token_name) < 50:  # Reasonable length for a token name
                    # Set the full name
                    metrics['token_full_name'] = token_name

                    # If we don't have a token symbol yet, create one from the name
                    if not metrics.get('token_name'):
                        words = token_name.split()
                        if words:
                            if len(words) > 1:
                                # Use initials for multi-word names
                                symbol = ''.join(word[0] for word in words if word)
                            else:
                                # Use first 4 chars for single-word names
                                symbol = words[0][:4]
                            metrics['token_name'] = symbol.upper()
                    break

        # Try specific volume patterns first (more accurate)
        # Check for TXs/Vol pattern
        txs_vol_match = TXS_VOL_REGEX.search(message_text)
        if txs_vol_match:
            metrics['txs'] = txs_vol_match.group(1)
            vol_value = txs_vol_match.group(2)
            # Keep the suffix (K, M, B) for display
            if 'K' in message_text[txs_vol_match.end()-5:txs_vol_match.end()]:
                vol_value += 'K'
            elif 'M' in message_text[txs_vol_match.end()-5:txs_vol_match.end()]:
                vol_value += 'M'
            elif 'B' in message_text[txs_vol_match.end()-5:txs_vol_match.end()]:
                vol_value += 'B'
            metrics['volume'] = vol_value
        else:
            # Check for the format "5m TXs/Vol: 237 / $26.5K"
            txs_vol_new_match = re.search(r"5m\s+TXs/Vol:\s*(\d+)\s*/\s*\$?([\d.,]+)(?:K|M|B)?", message_text, re.IGNORECASE)
            if txs_vol_new_match:
                metrics['txs'] = txs_vol_new_match.group(1)
                vol_value = txs_vol_new_match.group(2)
                # Keep the suffix (K, M, B) for display
                if 'K' in message_text[txs_vol_new_match.end()-5:txs_vol_new_match.end()]:
                    vol_value += 'K'
                elif 'M' in message_text[txs_vol_new_match.end()-5:txs_vol_new_match.end()]:
                    vol_value += 'M'
                elif 'B' in message_text[txs_vol_new_match.end()-5:txs_vol_new_match.end()]:
                    vol_value += 'B'
                metrics['volume'] = vol_value
                logger.debug(f"Extracted TXs/Vol from new format: {metrics['txs']} / ${vol_value}")
            else:
                volume_specific_match = VOLUME_SPECIFIC_REGEX.search(message_text)
                if volume_specific_match:
                    metrics['volume'] = volume_specific_match.group(1)
                else:
                    volume_5m_match = VOLUME_5M_REGEX.search(message_text)
                    if volume_5m_match:
                        metrics['txs'] = volume_5m_match.group(1)
                        metrics['volume'] = volume_5m_match.group(2) + 'K'
                    else:
                        # Extract volume with general pattern
                        volume_match = VOLUME_REGEX.search(message_text)
                        if volume_match:
                            metrics['volume'] = volume_match.group(1)

        # Extract price change information from the format "5m | 1h | 6h: 213.9% | 941.7% | 941.7%"
        price_change_match = re.search(r"5m\s*\|\s*1h\s*\|\s*6h:\s*([\d.,]+)%\s*\|\s*([\d.,]+)%\s*\|\s*([\d.,]+)%", message_text, re.IGNORECASE)
        if price_change_match:
            metrics['price_change_5m'] = price_change_match.group(1)
            metrics['price_change_1h'] = price_change_match.group(2)
            metrics['price_change_6h'] = price_change_match.group(3)
            logger.debug(f"Extracted price changes: 5m: {metrics['price_change_5m']}%, 1h: {metrics['price_change_1h']}%, 6h: {metrics['price_change_6h']}%")

        # Try specific MCP pattern first
        mcp_specific_match = MCP_SPECIFIC_REGEX.search(message_text)
        if mcp_specific_match:
            metrics['mcp'] = mcp_specific_match.group(1)
        else:
            # Extract market cap with general pattern
            mcp_match = MCP_REGEX.search(message_text)
            if mcp_match:
                mcp_value = mcp_match.group(1)
                # Check if there's a K/M/B suffix in the original text
                match_end = mcp_match.end()
                if match_end < len(message_text) and match_end + 1 < len(message_text):
                    suffix_text = message_text[match_end:match_end+2]
                    if 'K' in suffix_text.upper():
                        mcp_value += 'K'
                    elif 'M' in suffix_text.upper():
                        mcp_value += 'M'
                    elif 'B' in suffix_text.upper():
                        mcp_value += 'B'

                # Also check for "MCAP: 87K" format
                if 'K' not in mcp_value and 'M' not in mcp_value and 'B' not in mcp_value:
                    mcap_match = re.search(r'MCAP:\s*(\d+)K', message_text, re.IGNORECASE)
                    if mcap_match:
                        mcp_value = mcap_match.group(1) + 'K'

                metrics['mcp'] = mcp_value

        # Extract FDV
        fdv_match = FDV_REGEX.search(message_text)
        if fdv_match:
            metrics['fdv'] = fdv_match.group(1)

        # Extract FDV percent
        fdv_percent_match = FDV_PERCENT_REGEX.search(message_text)
        if fdv_percent_match:
            metrics['fdv_percent'] = fdv_percent_match.group(1)

        # Try specific liquidity pattern first
        liq_specific_match = LIQ_SPECIFIC_REGEX.search(message_text)
        if liq_specific_match:
            metrics['liquidity_usd'] = liq_specific_match.group(1)
        else:
            # Extract liquidity in SOL with general pattern
            liquidity_match = LIQUIDITY_REGEX.search(message_text)
            if liquidity_match:
                metrics['liquidity_sol'] = liquidity_match.group(1)

            # Extract liquidity in USD with general pattern
            liquidity_usd_match = LIQUIDITY_USD_REGEX.search(message_text)
            if liquidity_usd_match:
                metrics['liquidity_usd'] = liquidity_usd_match.group(1)

        # Try specific holder pattern first
        holder_specific_match = HOLDER_SPECIFIC_REGEX.search(message_text)
        if holder_specific_match:
            metrics['holders'] = holder_specific_match.group(1)
        else:
            # Extract holders with general pattern
            holders_match = HOLDERS_REGEX.search(message_text)
            if holders_match:
                metrics['holders'] = holders_match.group(1)

        # Try specific open time pattern first
        open_specific_match = OPEN_SPECIFIC_REGEX.search(message_text)
        if open_specific_match:
            metrics['open'] = open_specific_match.group(1) + 's'
        else:
            # Extract open time with general pattern
            open_match = OPEN_REGEX.search(message_text)
            if open_match:
                metrics['open'] = open_match.group(1)
                # Add unit if not present
                if metrics['open'].isdigit():
                    # Look for unit in the matched text
                    if 'min' in message_text or 'm' in message_text:
                        metrics['open'] += 'm'
                    elif 's' in message_text:
                        metrics['open'] += 's'
                    elif 'h' in message_text:
                        metrics['open'] += 'h'

        # Extract status
        status_match = STATUS_REGEX.search(message_text)
        if status_match:
            metrics['status'] = status_match.group(1)

        # Extract transactions if not already found
        if not metrics.get('txs'):
            txs_match = TXS_REGEX.search(message_text)
            if txs_match:
                metrics['txs'] = txs_match.group(1)

        # Look for Age in the format "Age: 2m"
        age_match = re.search(r'Age:\s*(\d+)([msh])', message_text, re.IGNORECASE)
        if age_match and not metrics.get('open'):
            metrics['open'] = age_match.group(1) + age_match.group(2)

        # Log extracted metrics for debugging
        if metrics:
            logger.debug(f"Extracted metrics from message: {metrics}")

        return metrics

    def _parse_numeric_value(self, value_str: str) -> float:
        """
        Parse a numeric value from a string that might include K, M, B suffixes.

        Args:
            value_str: String representation of a number, e.g., "10K", "1.5M", "$500", "26.4K", "27.93", "13.9"

        Returns:
            Numeric value as a float
        """
        if not value_str:
            return 0.0

        try:
            # Remove $ and commas
            clean_str = value_str.replace('$', '').replace(',', '').strip()

            # For financial metrics (market cap, liquidity, volume, FDV), values like "13.9" should be interpreted as "13.9K"
            # This is a common convention in crypto where small decimal values without suffix are assumed to be in thousands

            # Handle K, M, B suffixes first
            multiplier = 1
            if clean_str.upper().endswith('K'):
                multiplier = 1000
                clean_str = clean_str[:-1]
            elif clean_str.upper().endswith('M'):
                multiplier = 1000000
                clean_str = clean_str[:-1]
            elif clean_str.upper().endswith('B'):
                multiplier = 1000000000
                clean_str = clean_str[:-1]
            # Special case for values without suffix
            elif '.' in clean_str and not any(suffix in clean_str.upper() for suffix in ['K', 'M', 'B']):
                try:
                    float_val = float(clean_str)
                    # For financial metrics, if the value is small (< 1000) and has decimals, assume it's in thousands
                    # This handles cases like "13.9" (should be 13,900) and "25.69" (should be 25,690)
                    if float_val < 1000:
                        # Log the interpretation for debugging
                        logger.debug(f"Financial metric: Interpreting '{value_str}' as {float_val * 1000:,.0f} (assuming K suffix)")
                        return float_val * 1000
                except ValueError:
                    pass  # Continue with normal parsing

            # Convert to float and apply multiplier
            result = float(clean_str) * multiplier

            # Log the parsed value for debugging
            if multiplier > 1:
                logger.debug(f"Parsed '{value_str}' as {result:,.0f} (with {multiplier} multiplier)")

            return result
        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to parse numeric value '{value_str}': {e}")
            return 0.0

    def _calculate_sentiment(self, token_address: str, data: Dict[str, Any]) -> float:
        """Calculate sentiment score based on token metrics

        Args:
            token_address: The token address to analyze
            data: Token data from API sources

        Returns:
            Sentiment score between 0.0 and 1.0
        """
        # This is a placeholder - implement actual sentiment analysis
        # Parameters are intentionally unused in this version
        # but kept for future implementation

        # Suppress unused parameter warnings by accessing the parameters
        _ = token_address
        _ = data

        return 0.5

    async def close_sessions(self):
        """Close HTTP sessions - alias for cleanup method"""
        await self.cleanup()

    async def cleanup_old_cache_entries(self):
        """Clean up old cache entries to prevent memory leaks"""
        try:
            current_time = time.time()

            # Clean up price history (keep only last 100 entries per token)
            for token in list(self.price_history.keys()):
                if len(self.price_history[token]) > 100:
                    self.price_history[token] = self.price_history[token][-100:]

            # Clean up volume history (keep only last 100 entries per token)
            for token in list(self.volume_history.keys()):
                if len(self.volume_history[token]) > 100:
                    self.volume_history[token] = self.volume_history[token][-100:]

            # Clean up old price updates (older than 1 hour)
            for token in list(self.last_price_update.keys()):
                if current_time - self.last_price_update[token] > 3600:
                    del self.last_price_update[token]
                    if token in self.price_data:
                        del self.price_data[token]

            # Clean up rate limiter cache
            if hasattr(self, 'rate_limiter') and self.rate_limiter:
                try:
                    self.rate_limiter.clear_cache()
                except Exception as e:
                    logger.warning(f"Error clearing rate limiter cache: {e}")

            logger.debug("Cleaned up old cache entries")

        except Exception as e:
            logger.error(f"Error during cache cleanup: {e}")

    async def cleanup(self):
        """Cleanup resources with proper error handling"""
        try:
            # Close the main session if it exists and is not already closed
            if self.session and not self.session.closed:
                try:
                    await self.session.close()
                    logger.info("Closed main aiohttp session")
                except Exception as e:
                    logger.error(f"Error closing main session: {e}")

            # Moralis client removed

            # Clear all caches and data structures
            self.price_data.clear()
            self.last_price_update.clear()
            self.watchlist_tokens.clear()

            # Clear sessions dictionary
            self.sessions.clear()

            # Clear rate limiter cache
            if hasattr(self, 'rate_limiter') and self.rate_limiter:
                try:
                    if hasattr(self.rate_limiter, 'clear_cache'):
                        self.rate_limiter.clear_cache()
                        logger.info("Cleared rate limiter cache")
                    else:
                        logger.debug("Rate limiter has no clear_cache method")
                except Exception as e:
                    logger.error(f"Error clearing rate limiter cache: {e}")

            logger.info("TokenAnalyzer cleanup completed")
        except Exception as e:
            logger.error(f"Error during TokenAnalyzer cleanup: {e}", exc_info=True)

    def update_price_data(self, token_address: str, price: float, volume: float = None):
        """Update price and volume data for a token from WebSocket or other real-time sources"""
        if not token_address or price <= 0:
            return False

        try:
            # Update current price and timestamp
            self.price_data[token_address] = price
            self.last_price_update[token_address] = time.time()

            # Update volume if provided
            if volume is not None and volume >= 0:
                self.volume_data[token_address] = volume

            # Update price history
            if token_address not in self.price_history:
                self.price_history[token_address] = []
            self.price_history[token_address].append(price)

            # Trim history if too long
            if len(self.price_history[token_address]) > self.max_history_length:
                self.price_history[token_address] = self.price_history[token_address][-self.max_history_length:]

            # Update volume history if volume provided
            if volume is not None:
                if token_address not in self.volume_history:
                    self.volume_history[token_address] = []
                self.volume_history[token_address].append(volume)

                # Trim history if too long
                if len(self.volume_history[token_address]) > self.max_history_length:
                    self.volume_history[token_address] = self.volume_history[token_address][-self.max_history_length:]

            return True
        except Exception as e:
            logger.error(f"Error updating price data for {token_address}: {e}")
            return False

    def get_price_history(self, token_address: str) -> List[float]:
        """Get price history for a token"""
        return self.price_history.get(token_address, [])

    def get_volume_history(self, token_address: str) -> List[float]:
        """Get volume history for a token"""
        return self.volume_history.get(token_address, [])

    async def get_current_price(self, token_address: str, force_fresh: bool = False) -> float:
        """
        Get the current price of a token.

        Args:
            token_address: The token address to get the price for
            force_fresh: Whether to bypass cache and force fresh data (default: False)

        Returns:
            Current price as a float, or 0.0 if not available
        """
        try:
            # Use the analyze method with force_fresh parameter
            analysis = await self.analyze(token_address, force_fresh=force_fresh)
            if analysis and analysis.get('exists', False):
                return analysis.get('price', 0.0)
            return 0.0
        except Exception as e:
            logger.error(f"Error getting current price for {token_address}: {e}")
            return 0.0

    def format_token_metrics(self, metrics: Dict[str, Any]) -> str:
        """Format token metrics for display with improved error handling"""
        if not metrics:
            return "No metrics available"

        lines = []
        lines.append("=== Token Analysis Results ===")

        if not metrics.get('exists', False):
            lines.append(f"Analysis Failed: {metrics.get('reason', 'Unknown reason')}")
            return "\n".join(lines)

        # Safe extraction with defaults
        try:
            # Token identification
            name = metrics.get('name', 'Unknown')
            symbol = metrics.get('symbol', 'Unknown')
            lines.append(f"Token: {name} ({symbol})")

            # Price and source
            price = metrics.get('price', 0)
            price_str = f"${price:.8f}" if price else "Unknown"
            lines.append(f"Price: {price_str}")

            source = metrics.get('price_source', 'Unknown')
            lines.append(f"Source: {source}")

            confidence = metrics.get('confidence', 0)
            lines.append(f"Confidence: {confidence:.2f}")

            sentiment = metrics.get('sentiment', 0)
            lines.append(f"Sentiment: {sentiment:.2f}")

            # Market metrics
            market_cap = metrics.get('market_cap', 0)
            if market_cap:
                lines.append(f"Market Cap: ${market_cap:,.2f}")

            fdv = metrics.get('fdv', 0)
            if fdv:
                lines.append(f"Fully Diluted Value: ${fdv:,.2f}")

            fdv_change_5m = metrics.get('fdv_change_5m', 0)
            if fdv_change_5m:
                lines.append(f"FDV Change (5m): {fdv_change_5m:.2f}%")

            # Liquidity metrics
            liquidity_usd = metrics.get('liquidity_usd', 0)
            if liquidity_usd:
                lines.append(f"Liquidity (USD): ${liquidity_usd:,.2f}")

            liquidity_sol = metrics.get('liquidity_sol', 0)
            if liquidity_sol:
                lines.append(f"Liquidity (SOL): {liquidity_sol:,.2f} SOL")

            # Volume metrics
            volume_5m = metrics.get('volume_5m', 0)
            if volume_5m:
                lines.append(f"5m Volume: ${volume_5m:,.2f}")

            volume_1h = metrics.get('volume_1h', 0)
            if volume_1h:
                lines.append(f"1h Volume: ${volume_1h:,.2f}")

            volume_24h = metrics.get('volume_24h', 0)
            if volume_24h:
                lines.append(f"24h Volume: ${volume_24h:,.2f}")

            # Age metrics
            pair_age = metrics.get('pair_age_minutes', 0)
            if pair_age:
                hours = pair_age // 60
                minutes = pair_age % 60
                if hours > 0:
                    lines.append(f"Pair Age: {hours}h {minutes}m")
                else:
                    lines.append(f"Pair Age: {minutes}m")

            # Transaction metrics
            tx_count = metrics.get('tx_count_24h', 0)
            if tx_count:
                lines.append(f"24h Transactions: {tx_count:,}")

            # Holder metrics
            holder_count = metrics.get('holder_count', 0)
            if holder_count:
                lines.append(f"Holder Count: {holder_count:,}")

            bundled_pct = metrics.get('bundled_pct', 0)
            if bundled_pct:
                lines.append(f"Top Holder %: {bundled_pct:.2f}%")

        except Exception as e:
            logger.error(f"Error formatting token metrics: {e}")
            lines.append(f"Error formatting metrics: {str(e)}")

        lines.append("===========================")
        return "\n".join(lines)

    def get_cached_token_details(self, token_address: str) -> Dict[str, Any]:
        """Get token details from cache if available."""
        if not token_address:
            return {}

        # Check DexScreener cache
        dex_cache_key = f"dexscreener_token_{token_address}"
        if dex_cache_key in self.rate_limiter.cache:
            return self.rate_limiter.cache[dex_cache_key]['data']

        # Moralis cache removed

        return {}

    def get_cached_token_data(self, token_address: str) -> Dict[str, Any]:
        """Get cached token data for a token address.

        This method is used by BotController to get price and volume history.

        Args:
            token_address: The token address to get cached data for

        Returns:
            Dict containing cached token data or empty dict if not in cache
        """
        logger.debug(f"Getting cached token data for {token_address}")

        # Check if token has 'pump' suffix and log it
        if token_address.lower().endswith('pump'):
            logger.debug(f"Token {token_address} has 'pump' suffix - special handling may be needed")

        # First check in price_history and volume_history
        if token_address in self.price_history:
            logger.debug(f"Found token {token_address} in price_history")
            result = {
                "price_history": self.price_history.get(token_address, []),
                "volume_history": self.volume_history.get(token_address, [])
            }
            return result

        # Then check rate limiter cache for DexScreener data
        dex_cache_key = f"dexscreener_token_{token_address}"
        if hasattr(self, 'rate_limiter') and dex_cache_key in self.rate_limiter.cache:
            logger.debug(f"Found token {token_address} in DexScreener cache")
            dex_data = self.rate_limiter.cache[dex_cache_key]['data']

            # Extract pairs data if available
            if 'pairs' in dex_data:
                pairs = dex_data['pairs']
                if pairs and len(pairs) > 0:
                    # Create a structured result with the data BotController expects
                    result = {
                        "raw_data": {
                            "dexscreener": dex_data
                        },
                        "price_history": [float(pairs[0].get('priceUsd', 0))],
                        "volume_history": [float(pairs[0].get('volume', {}).get('h1', 0) / 12)]  # Estimate 5m volume
                    }
                    return result

            # If we have dex_data but not in the expected format, return it as is
            return {"raw_data": {"dexscreener": dex_data}}

        # Check for cached analysis results from previous analyze() calls
        for cache_key in self.rate_limiter.cache:
            if token_address in cache_key:
                logger.debug(f"Found token {token_address} in cache with key {cache_key}")
                return self.rate_limiter.cache[cache_key]['data']

        # If nothing found, return empty dict
        logger.debug(f"No cached data found for token {token_address}")
        return {}

    def get_api_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get API usage statistics."""
        stats = {}

        for provider, provider_stats in self.rate_limiter.stats.items():
            stats[provider] = {
                'total': provider_stats.get('requests', 0),
                'success': provider_stats.get('successes', 0),
                'failure': provider_stats.get('failures', 0),
                'avg_time': sum(provider_stats.get('response_times', [])) / max(1, len(provider_stats.get('response_times', [])))
            }

        return stats

    async def check_api_health(self) -> Dict[str, str]:
        """Check the health/status of configured APIs."""
        logger.info("Checking API health status...")
        status = {}

        # Initialize sessions if needed
        if not self.session or self.session.closed:
            await self.init_sessions()

        # Test DexScreener API
        try:
            # Use a known Solana token for testing
            test_token = "So11111111111111111111111111111111111111112"  # Wrapped SOL
            dex_data = await self.get_dex_screener_data(test_token)
            if dex_data and dex_data.get('exists', False):
                status["dexscreener"] = "OK"
            else:
                status["dexscreener"] = "Degraded - No data returned"
        except Exception as e:
            logger.error(f"Error checking DexScreener API: {e}")
            status["dexscreener"] = f"Error: {str(e)[:50]}..."

        # Moralis API removed
        status["moralis"] = "Removed"

        return status

    async def make_trading_decision(self, market_data: Dict, signal_metadata: Dict) -> Dict:
        """
        Make trading decision based on market data from simple_pump_analyzer
        PRESERVES ALL EXISTING TRADING LOGIC - just receives data instead of fetching it
        """
        try:
            # Extract token address from market data or signal metadata
            token_address = signal_metadata.get('token_address') or market_data.get('token_address')
            if not token_address:
                return {"confidence": 0.0, "reason": "No token address provided"}

            # Use the existing confidence calculation logic with market data
            confidence_result = await self._calculate_confidence_from_data(market_data, signal_metadata)

            # Add trading decision fields to the market data
            market_data.update({
                "confidence": confidence_result.get("confidence", 0.0),
                "confidence_factors": confidence_result.get("factors", {}),
                "trading_decision": confidence_result.get("decision", "HOLD"),
                "risk_level": confidence_result.get("risk_level", "HIGH"),
                "recommended_action": confidence_result.get("action", "WAIT")
            })

            logger.info(f"Trading decision for {token_address}: {confidence_result.get('confidence', 0.0):.2f} confidence")
            return market_data

        except Exception as e:
            logger.error(f"Error in trading decision: {e}")
            return {"confidence": 0.0, "reason": f"Trading decision error: {str(e)}"}

    async def _calculate_confidence_from_data(self, market_data: Dict, signal_metadata: Dict) -> Dict:
        """
        Calculate confidence using existing logic but with pre-fetched market data
        This preserves all the sophisticated trading logic from token_analyzer
        """
        try:
            # Initialize confidence factors
            factors = {}
            total_confidence = 0.0

            # Extract key metrics from market data
            price = market_data.get('price', 0)
            market_cap = market_data.get('market_cap', 0)
            liquidity_usd = market_data.get('liquidity_usd', 0)
            volume_24h = market_data.get('volume_24h', 0)
            volume_5m = market_data.get('volume_5m', 0)
            holder_count = market_data.get('holder_count', 0)

            # Apply existing confidence logic (simplified version)
            # Market cap factor
            if 10000 <= market_cap <= 1000000:  # Sweet spot for pump.fun
                factors['market_cap'] = 0.2
                total_confidence += 0.2
            elif market_cap > 1000000:
                factors['market_cap'] = 0.1
                total_confidence += 0.1

            # Liquidity factor
            if liquidity_usd > 50000:
                factors['liquidity'] = 0.15
                total_confidence += 0.15
            elif liquidity_usd > 20000:
                factors['liquidity'] = 0.1
                total_confidence += 0.1

            # Volume factor
            if volume_5m > 1000:
                factors['volume_5m'] = 0.15
                total_confidence += 0.15

            # Holder count factor
            if holder_count > 100:
                factors['holders'] = 0.1
                total_confidence += 0.1

            # Signal source factor (from memories)
            if signal_metadata.get('is_gmgn_channel', False):
                factors['signal_source'] = 0.2
                total_confidence += 0.2

            # Pump.fun bonus (from memories)
            token_address = signal_metadata.get('token_address', '')
            if token_address.endswith('pump'):
                factors['pump_fun'] = 0.2
                total_confidence += 0.2

            # Determine trading decision
            if total_confidence >= 0.7:
                decision = "BUY"
                action = "EXECUTE_TRADE"
                risk_level = "LOW"
            elif total_confidence >= 0.5:
                decision = "CONSIDER"
                action = "MONITOR"
                risk_level = "MEDIUM"
            else:
                decision = "AVOID"
                action = "SKIP"
                risk_level = "HIGH"

            return {
                "confidence": min(total_confidence, 1.0),
                "factors": factors,
                "decision": decision,
                "action": action,
                "risk_level": risk_level
            }

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return {"confidence": 0.0, "factors": {}, "decision": "AVOID", "action": "SKIP", "risk_level": "HIGH"}

    # Add a __del__ method to ensure cleanup even if not called explicitly
    def __del__(self):
        """Destructor to ensure session cleanup"""
        if self.session and not self.session.closed:
            # We can't await in __del__, so we need to use a different approach
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.session.close())
                else:
                    loop.run_until_complete(self.session.close())
            except Exception:
                pass  # Ignore errors in destructor

# Test function - commented out to avoid accidental execution
# async def test_analyzer():
#     from config_manager import ConfigManager
#     config = ConfigManager()
#     analyzer = TokenAnalyzer(config)
#     # Test with a known Solana token address
#     result = await analyzer.analyze("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")  # USDC on Sol
#     print(f"Analysis result: {result}")
#     await analyzer.cleanup()
