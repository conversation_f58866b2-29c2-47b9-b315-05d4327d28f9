#!/usr/bin/env python3
"""
CRITICAL FIX: Ensure sell execution is working
This script will:
1. Check if execution queue is running
2. Start it if not running
3. Force sell any positions that should have been sold
4. Verify the sell pipeline is working
"""

import asyncio
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_sell_execution():
    """Fix the sell execution pipeline"""
    try:
        print("🚨 CRITICAL SELL EXECUTION FIX")
        print("=" * 50)
        
        # Import required modules
        from config_manager import ConfigManager
        from state_manager import StateManager
        from trade_executor import TradeExecutor
        from execution_queue import TradeExecutionQueue
        from bot_controller import BotController
        
        print("✅ All modules imported successfully")
        
        # Initialize components
        config = ConfigManager()
        state = StateManager(config)
        
        print("✅ Config and State managers initialized")
        
        # Check current positions
        open_positions = state.get_open_positions()
        print(f"📊 Current open positions: {len(open_positions)}")
        
        if not open_positions:
            print("ℹ️ No open positions found - nothing to sell")
            return True
        
        # Initialize bot controller to get access to execution queue
        bot_controller = BotController(config, state)
        await bot_controller.initialize(enable_signal_processing=False)
        
        print("✅ Bot controller initialized")
        
        # Check execution queue status
        execution_queue = bot_controller.execution_queue
        print(f"🔄 Execution queue running: {execution_queue.is_running}")
        
        # CRITICAL FIX: Start execution queue if not running
        if not execution_queue.is_running:
            print("🚨 EXECUTION QUEUE NOT RUNNING - STARTING NOW")
            await execution_queue.start()
            print("✅ Execution queue started")
            
            # Wait a moment for workers to initialize
            await asyncio.sleep(1)
            print(f"🔄 Execution queue running: {execution_queue.is_running}")
        
        # Check positions that should be sold
        current_time = time.time()
        max_hold_time_minutes = config.get_section('trading_settings').get('max_hold_time_minutes', 3)
        max_hold_time_seconds = max_hold_time_minutes * 60
        
        print(f"\n🔍 CHECKING POSITIONS FOR FORCED SELLS")
        print(f"⏰ Max hold time: {max_hold_time_minutes} minutes")
        
        positions_to_force_sell = []
        
        for token, position in open_positions.items():
            purchase_time = position.get('purchase_time', current_time)
            age_seconds = current_time - purchase_time
            age_minutes = age_seconds / 60
            
            print(f"\n📊 Position: {token}")
            print(f"   Age: {age_minutes:.1f} minutes")
            print(f"   Status: {position.get('status', 'unknown')}")
            print(f"   Token amount: {position.get('token_amount', 0):.6f}")
            
            # Check if position should be force-sold
            if age_seconds >= max_hold_time_seconds:
                print(f"   🚨 SHOULD BE SOLD: Exceeds max hold time")
                positions_to_force_sell.append((token, position))
            else:
                remaining_time = max_hold_time_seconds - age_seconds
                print(f"   ⏳ Time remaining: {remaining_time/60:.1f} minutes")
        
        # Force sell positions that should have been sold
        if positions_to_force_sell:
            print(f"\n🚨 FORCE SELLING {len(positions_to_force_sell)} POSITIONS")
            
            for token, position in positions_to_force_sell:
                print(f"\n🔥 FORCE SELLING: {token}")
                
                try:
                    # Queue emergency sell
                    sell_queued = await execution_queue.queue_sell(
                        token_address=token,
                        token_amount=position.get('token_amount', 0),
                        entry_price=position.get('entry_price', 0),
                        slippage_bps=1000,  # 10% emergency slippage
                        sell_fraction=1.0,
                        priority=0,  # Emergency priority
                        event_id=f"force_sell_{token}_{int(current_time)}"
                    )
                    
                    if sell_queued:
                        print(f"✅ Emergency sell queued for {token}")
                    else:
                        print(f"❌ Failed to queue emergency sell for {token}")
                        
                except Exception as e:
                    print(f"❌ Error queuing emergency sell for {token}: {e}")
            
            # Wait for sells to process
            print(f"\n⏳ Waiting 30 seconds for sells to process...")
            await asyncio.sleep(30)
            
            # Check if positions were closed
            updated_positions = state.get_open_positions()
            print(f"\n📊 Positions after force sell: {len(updated_positions)}")
            
            for token, position in positions_to_force_sell:
                if token not in updated_positions:
                    print(f"✅ Successfully sold: {token}")
                else:
                    print(f"❌ Still open: {token}")
        
        else:
            print("✅ No positions need force selling")
        
        # Start position monitoring if not running
        if hasattr(bot_controller, '_monitor_task'):
            if bot_controller._monitor_task is None or bot_controller._monitor_task.done():
                print("\n🔄 Starting position monitoring...")
                await bot_controller._start_position_monitoring_resilient()
                print("✅ Position monitoring started")
            else:
                print("✅ Position monitoring already running")
        
        print("\n✅ SELL EXECUTION FIX COMPLETE")
        print("=" * 35)
        print("The execution queue is now running and will process sells.")
        print("Position monitoring is active and will trigger sells when conditions are met.")
        
        # Keep the script running for a bit to monitor
        print("\n🔍 Monitoring for 60 seconds...")
        for i in range(12):
            await asyncio.sleep(5)
            current_positions = state.get_open_positions()
            print(f"   Positions: {len(current_positions)} | Queue running: {execution_queue.is_running}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in sell execution fix: {e}")
        logger.error(f"Sell execution fix failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    asyncio.run(fix_sell_execution())
